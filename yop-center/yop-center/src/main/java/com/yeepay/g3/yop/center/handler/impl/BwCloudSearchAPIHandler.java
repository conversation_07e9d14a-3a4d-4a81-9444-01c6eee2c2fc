package com.yeepay.g3.yop.center.handler.impl;

import com.baiwang.bop.client.IBopClient;
import com.baiwang.bop.client.impl.BopRestClient;
import com.baiwang.bop.request.impl.bizinfo.CompanySearchRequest;
import com.baiwang.bop.respose.entity.bizinfo.CompanySearchResponse;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.AbstractBwCloudAPIHandler;
import com.yeepay.g3.yop.center.handler.entity.BWCloudResult;
import com.yeepay.g3.yop.center.handler.entity.BwConfig;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: BwCloudIssueAPIHandler<br/>
 * description: 百望云单个公司查询接口<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年9月21日 上午11:22:07
 */
@Component
public class BwCloudSearchAPIHandler extends AbstractBwCloudAPIHandler<CompanySearchResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BwCloudSearchAPIHandler.class);

    @Override
    protected BWCloudResult<CompanySearchResponse> execute(BwConfig bwConfig, String token, Map<String, Object> params) {
        String serviceUrl = bwConfig.getServerUrl();
        String appKey = bwConfig.getAppKey();
        String appSecret = bwConfig.getAppSecret();
        String appId = bwConfig.getAppId();
        BWCloudResult<CompanySearchResponse> result = new BWCloudResult<CompanySearchResponse>();
        IBopClient client = new BopRestClient(serviceUrl, appKey, appSecret);
        CompanySearchRequest request = (CompanySearchRequest) params.get("companySearchRequest");
        request.setAccuracy("true");
        request.setAppId(appId);
        LOGGER.info("百望云单个公司查询请求参数{}.", JSON_MAPPER.toJson(request));
        CompanySearchResponse response = client.execute(request, token, CompanySearchResponse.class);
        result.setReturnCode(RES_SUCCESS);
        result.setReturnMessages("响应成功");
        result.setReturnData(response);
        return result;
    }

}
