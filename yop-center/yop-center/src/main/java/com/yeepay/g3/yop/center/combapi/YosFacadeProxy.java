package com.yeepay.g3.yop.center.combapi;

import com.yeepay.g3.yop.frame.context.Context;
import org.springframework.stereotype.Component;

/**
 * title: YosFacade代理<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-03-28 16:11
 */
@Component
public class YosFacadeProxy {

    /**
     * 下载
     *
     * @param context 上下文
     * @return 流
     */
    public String download(Context context) {
        //do nothing
        return null;
    }
}
