/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.handler;

import com.yeepay.g3.yop.center.handler.entity.FileUploadResult;
import com.yeepay.g3.yop.frame.context.Context;
import org.springframework.web.multipart.MultipartFile;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/12/13 下午6:36
 */
public interface FileUploadHandler {

    FileUploadResult upload(MultipartFile file, Context context);
}
