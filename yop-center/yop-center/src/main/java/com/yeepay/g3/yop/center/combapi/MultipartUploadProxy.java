/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi;

import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.sys.dto.storeconfig.CephStoreConfig;
import com.yeepay.g3.sdk.yop.http.Headers;
import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.dto.*;
import com.yeepay.g3.yop.center.storage.model.ResponseHandler;
import com.yeepay.g3.yop.center.storage.model.Result;
import com.yeepay.g3.yop.frame.CharacterConstants;
import com.yeepay.g3.yop.frame.cache.MultipartStorageConfigLocalCache;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.exception.storage.FileStoreAuthFailedException;
import com.yeepay.g3.yop.frame.exception.storage.FileStoreUnavailableException;
import com.yeepay.g3.yop.frame.exception.storage.FileStoreUploadFailedException;
import com.yeepay.g3.yop.frame.exception.storage.IspFileStoreNotConfiguredException;
import com.yeepay.g3.yop.frame.storage.CephUploadRequest;
import com.yeepay.g3.yop.frame.storage.FileMetadata;
import com.yeepay.g3.yop.frame.storage.InternalExpressionResolver;
import com.yeepay.g3.yop.frame.storage.Storage;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import com.yeepay.storage.sdk.constants.HttpHeaders;
import com.yeepay.storage.sdk.exception.StorageException;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.yeepay.storage.sdk.constants.CommonConstants.DEFAULT_ACCEPT;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/16 下午4:11
 */
@Component
public class MultipartUploadProxy {

    private static final Logger LOGGER = LoggerFactory.getLogger(MultipartUploadProxy.class);

    private ConcurrentHashMap<String, HttpClient> clientMap = new ConcurrentHashMap<>();

    private static final String BASIC_TYPE = "Basic";

    private static final String EMPTY_STRING = " ";

    private static final String KEY = "key";

    private static final String UPLOAD_ID = "uploadId";

    private static final String PART_NUMBER = "partNumber";

    private static final String PART_SIZE = "partSize";

    private static final String HTTP = "http://";

    private static final String STORAGE_URL_PREFIX = "/api/v2/";

    private static final String STORAGE_URL_INIT = "/multipart/init";

    private static final String STORAGE_URL_UPLOAD = "/multipart/upload";

    private static final String STORAGE_URL_COMPLETE = "/multipart/complete";

    private static final String STORAGE_URL_ABORT = "/multipart/abort";

    @Autowired
    private MultipartStorageConfigLocalCache multipartStorageConfigLocalCache;

    @Autowired
    private InternalExpressionResolver internalExpressionResolver;

    @Autowired
    private Storage storage;

    private JsonMapper jsonMapper = JsonMapper.nonNullMapper();

    public String upload(String file) {
        return file;
    }

    public MultipartInitResult init(MultipartInitParam param, Context context) {
        LOGGER.info(param.toString());
        // 根据bizCode获取CEPH的配置
        String bizCode = param.getBizCode();
        Map<String, String> bizCodeConfig = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_MULTIPART_API_BIZ_CODE_CONFIG, Map.class);
        String bucket = bizCodeConfig.get(bizCode);
        CephStoreConfig cephStoreConfig = multipartStorageConfigLocalCache.get(bucket);
        if (null == cephStoreConfig) {
            throw new IspFileStoreNotConfiguredException();
        }

        // 生成文件名
        String key = internalExpressionResolver.resolve(cephStoreConfig.getFileName(), context);
        String fileExt = StringUtils.defaultIfEmpty(FilenameUtils.getExtension(param.getFileName()), "bin");
        key = key + CharacterConstants.DOT + fileExt;

        // 构造http请求
        StringBuilder url = new StringBuilder(HTTP + ConfigUtils.getSysConfigParam(ConfigEnum.YOP_YOS_CEPH_ADDRESS, String.class));
        url.append(STORAGE_URL_PREFIX).append(bucket).append(STORAGE_URL_INIT)
                .append("?")
                .append(KEY).append(CharacterConstants.EQUAL).append(key);
        HttpUriRequest httpUriRequest = RequestBuilder.post(url.toString())
                .addHeader(Headers.AUTHORIZATION, BASIC_TYPE + EMPTY_STRING + cephStoreConfig.getSecretKey())
                .build();
        // 调用CEPH
        Result cephResult = execute(bucket, httpUriRequest);

        // 封装结果
        String uploadId = (String) cephResult.getData().get(UPLOAD_ID);
        MultipartInitResult result = new MultipartInitResult();
        result.setBucket(bucket);
        result.setKey(key);
        result.setUploadId(uploadId);
        return result;
    }

    public MultipartUploadResult upload(MultipartUploadParam param, HttpServletRequest httpServletRequest) throws IOException {
        LOGGER.info(param.toString());
        // 根据bizCode获取CEPH的配置
        String bucket = param.getBucket();
        CephStoreConfig cephStoreConfig = multipartStorageConfigLocalCache.get(bucket);
        if (null == cephStoreConfig) {
            throw new IspFileStoreNotConfiguredException();
        }

        // 构造http请求
        StringBuilder url = new StringBuilder(HTTP + ConfigUtils.getSysConfigParam(ConfigEnum.YOP_YOS_CEPH_ADDRESS, String.class));
        url.append(STORAGE_URL_PREFIX).append(bucket).append(STORAGE_URL_UPLOAD)
                .append("?")
                .append(KEY).append(CharacterConstants.EQUAL).append(param.getKey())
                .append(CharacterConstants.AND).append(UPLOAD_ID).append(CharacterConstants.EQUAL).append(param.getUploadId())
                .append(CharacterConstants.AND).append(PART_NUMBER).append(CharacterConstants.EQUAL).append(param.getPartNumber())
                .append(CharacterConstants.AND).append(PART_SIZE).append(CharacterConstants.EQUAL).append(httpServletRequest.getContentLength());
        int contentLength = httpServletRequest.getContentLength();
        ServletInputStream inputStream = httpServletRequest.getInputStream();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(contentLength == -1 ? IOUtils.toByteArray(inputStream) : IOUtils.toByteArray(inputStream, contentLength));
        HttpUriRequest httpUriRequest = RequestBuilder.put(url.toString())
                .addHeader(Headers.AUTHORIZATION, BASIC_TYPE + EMPTY_STRING + cephStoreConfig.getSecretKey())
                .setEntity(new InputStreamEntity(byteArrayInputStream, byteArrayInputStream.available(), ContentType.APPLICATION_OCTET_STREAM))
                .build();

        // 调用CEPH
        Result cephResult = execute(bucket, httpUriRequest);

        // 封装结果
        Map<String, Object> data = cephResult.getData();
        String ETag = (String) data.get("etag");
        MultipartUploadResult result = new MultipartUploadResult();
        result.setETag(ETag);
        return result;
    }

    public MultipartCompleteResult complete(MultipartCompleteParam param) {
        LOGGER.info(param.toString());
        // 根据bizCode获取CEPH的配置
        String bucket = param.getBucket();
        CephStoreConfig cephStoreConfig = multipartStorageConfigLocalCache.get(bucket);
        if (null == cephStoreConfig) {
            throw new IspFileStoreNotConfiguredException();
        }

        // 构造http请求
        StringBuilder url = new StringBuilder(HTTP + ConfigUtils.getSysConfigParam(ConfigEnum.YOP_YOS_CEPH_ADDRESS, String.class));
        url.append(STORAGE_URL_PREFIX).append(bucket).append(STORAGE_URL_COMPLETE);
        HttpUriRequest httpUriRequest = RequestBuilder.post(url.toString())
                .addHeader(Headers.AUTHORIZATION, BASIC_TYPE + EMPTY_STRING + cephStoreConfig.getSecretKey())
                .setEntity(new StringEntity(jsonMapper.toJson(param), ContentType.APPLICATION_JSON))
                .build();

        // 调用CEPH
        Result cephResult = execute(bucket, httpUriRequest);

        // 封装结果
        Map<String, Object> data = cephResult.getData();
        String ETag = (String) data.get("etag");
        String location = (String) data.get("location");
        MultipartCompleteResult result = new MultipartCompleteResult();
        result.setBucket(param.getBucket());
        result.setKey(param.getKey());
        result.setLocation(location);
        result.setETag(ETag);
        return result;
    }

    public MultipartAbortResult abort(MultipartAbortParam param) {
        LOGGER.info(param.toString());
        // 根据bizCode获取CEPH的配置
        String bucket = param.getBucket();
        CephStoreConfig cephStoreConfig = multipartStorageConfigLocalCache.get(bucket);
        if (null == cephStoreConfig) {
            throw new IspFileStoreNotConfiguredException();
        }

        // 构造http请求
        String key = param.getKey();
        StringBuilder url = new StringBuilder(HTTP + ConfigUtils.getSysConfigParam(ConfigEnum.YOP_YOS_CEPH_ADDRESS, String.class));
        url.append(STORAGE_URL_PREFIX).append(bucket).append(STORAGE_URL_ABORT)
                .append("?")
                .append(KEY).append(CharacterConstants.EQUAL).append(key)
                .append(CharacterConstants.AND).append(UPLOAD_ID).append(CharacterConstants.EQUAL).append(param.getUploadId());
        HttpUriRequest httpUriRequest = RequestBuilder.delete(url.toString())
                .addHeader(Headers.AUTHORIZATION, BASIC_TYPE + EMPTY_STRING + cephStoreConfig.getSecretKey())
                .build();

        // 调用CEPH
        execute(bucket, httpUriRequest);

        // 封装结果
        return new MultipartAbortResult();
    }

    public UploadResult upload(UploadParam param, Context context) throws IOException {
        LOGGER.info(param.toString());
        // 根据bizCode获取CEPH的配置
        String bizCode = param.getBizCode();
        Map<String, String> bizCodeConfig = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_MULTIPART_API_BIZ_CODE_CONFIG, Map.class);
        String bucket = bizCodeConfig.get(bizCode);
        CephStoreConfig cephStoreConfig = multipartStorageConfigLocalCache.get(bucket);
        if (null == cephStoreConfig) {
            throw new IspFileStoreNotConfiguredException();
        }

        // 生成文件名
        String key = internalExpressionResolver.resolve(cephStoreConfig.getFileName(), context);
        String fileExt = StringUtils.defaultIfEmpty(FilenameUtils.getExtension(param.getFileName()), "bin");
        key = key + CharacterConstants.DOT + fileExt;
        HttpServletRequest rawRequest = context.getRawRequest();
        int contentLength = rawRequest.getContentLength();
        ServletInputStream inputStream = rawRequest.getInputStream();
        CephUploadRequest uploadRequest = CephUploadRequest.newBuilder()
                .secretKey(cephStoreConfig.getSecretKey())
                .bucket(bucket)
                .fileName(key)
                .inputStream(new ByteArrayInputStream(contentLength == -1 ? IOUtils.toByteArray(inputStream) : IOUtils.toByteArray(inputStream, contentLength)))
                .address(ConfigUtils.getSysConfigParam(ConfigEnum.YOP_YOS_CEPH_ADDRESS, String.class))
                .build();

        // 调用CEPH
        FileMetadata fileMetadata = storage.upload(uploadRequest);

        // 封装结果
        UploadResult result = new UploadResult();
        result.setBucket(bucket);
        result.setKey(key);
        result.setLocation(ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_CEPH_RESOURCE_PATH, String.class) + CharacterConstants.SLASH + bucket + CharacterConstants.SLASH + key);
        result.setETag(fileMetadata.getMd5());
        return result;
    }

    private Result execute(String bucket, HttpUriRequest httpUriRequest) {
        try {
            httpUriRequest.addHeader(HttpHeaders.ACCEPT, DEFAULT_ACCEPT);

            if (ThreadContextUtils.contextInitialized()) {
                httpUriRequest.addHeader(ThreadContextUtils.KEY_GUID, ThreadContextUtils.getContext().getThreadUID());
                String transferLevel = ThreadContextUtils.getContext().getTransferLevel();
                httpUriRequest.addHeader(ThreadContextUtils.KEY_TRANSFER_LEVEL, transferLevel);
            }
            HttpClient httpClient = buildHttpClient(bucket);
            HttpResponse response = httpClient.execute(httpUriRequest);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                return ResponseHandler.handle(response);
            } else {
                return ResponseHandler.handleError(response);
            }
        } catch (IOException e) {
            LOGGER.error("multipart upload error!", e);
            throw new FileStoreUnavailableException();
        } catch (StorageException e) {
            LOGGER.error("multipart upload error!", e);
            if (e.getCode() == HttpStatus.SC_FORBIDDEN) {
                throw new FileStoreAuthFailedException();
            }
            switch (e.getCode()) {
                case HttpStatus.SC_FORBIDDEN:
                    throw new FileStoreAuthFailedException();
                case HttpStatus.SC_BAD_REQUEST:
                    throw new FileStoreAuthFailedException();
                case HttpStatus.SC_NOT_FOUND:
                    throw new FileStoreUploadFailedException();
                default:
                    throw new FileStoreUnavailableException();
            }
        }

    }

    private HttpClient buildHttpClient(String bucket) {
        return clientMap.computeIfAbsent(bucket, s -> {
            Map<String, String> config = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_CEPH_HTTP_CLIENT_CONFIG, Map.class);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(Integer.parseInt(config.get("socketTimeout")))
                    .setConnectTimeout(Integer.parseInt(config.get("connectTimeout")))
                    .build();
            return HttpClientBuilder.create()
                    .setDefaultRequestConfig(requestConfig)
                    .setMaxConnTotal(Integer.parseInt(config.get("maxConnTotal")))
                    .setMaxConnPerRoute(Integer.parseInt(config.get("maxConnPerRoute")))
                    .build();
        });
    }
}
