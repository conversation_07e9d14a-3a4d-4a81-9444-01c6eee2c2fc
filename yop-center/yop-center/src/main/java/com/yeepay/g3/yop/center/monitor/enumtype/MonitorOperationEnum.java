package com.yeepay.g3.yop.center.monitor.enumtype;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.utils.NetworkUtils;
import com.yeepay.g3.facade.yop.sys.dto.AppDTO;
import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.ext.gateway.backend.handler.ApiHandler;
import com.yeepay.g3.yop.ext.gateway.context.api.impl.ApiContext;
import com.yeepay.g3.yop.ext.gateway.context.app.impl.AppContext;
import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContext;
import com.yeepay.g3.yop.ext.gateway.context.param.ParamContext;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateProtocolVersion;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.utils.MonitorLogUtils;
import com.yeepay.g3.yop.frame.utils.YopConstants;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import io.opentracing.Span;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 监控操作枚举
 */
public enum MonitorOperationEnum {

    REQUEST("REQUEST", "请求进入") {
        @Override
        protected void handleSpecialTags(Span span, ServerWebExchange exchange, Map<String, Object> map) {
            super.handleSpecialTags(span, exchange, map);
        }
    },
    RESPONSE("RESPONSE", "响应返回") {
        @Override
        protected void handleSpecialTags(Span span, ServerWebExchange exchange, Map<String, Object> map) {
            AppContext appContext = ServerWebExchangeUtils.getAppContext(exchange);
            boolean svip = false;
            boolean vip = false;
            if (null != appContext && null != appContext.getApp() && null != appContext.getApp().getCustomerNo()) {
                String customerNo = appContext.getApp().getCustomerNo();
                List<String> svipCustomer = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_SVIP_CUSTOMER, List.class);
                svip = svipCustomer.contains(customerNo);

                List<String> vipCustomers = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_VIP_CUSTOMER, List.class);
                vip = vipCustomers.contains(customerNo);
            }
            span.setTag("svip", svip ? 1 : 0);
            span.setTag("vip", vip ? 1 : 0);
            InternalException exception = (InternalException) map.get("exception");
            if (exception == null) {
                span.setTag("status", "SUCCESS");
            } else {
                span.setTag("status", "FAILED");
                span.setTag("errorCode", exception.getStatus().getCode());
                span.setTag("subErrorCode", exception.getCode());
            }
            Map properties = ServerWebExchangeUtils.getProperties(exchange);
            Object backendLatencyO = properties.get("backendLatency");
            int backendLatency = backendLatencyO == null ? -1 : Integer.parseInt(backendLatencyO.toString());
            long totalLatency = (long) map.get("totalLatency");
            span.setTag("backendLatency", backendLatency);
            span.setTag("totalLatency", totalLatency);
            span.setTag("platformLatency", totalLatency - backendLatency);
            span.setTag("enableNewInvokeChain", 1);
            String securityStrategy = "";
            String authVersion = "";
            AuthenticateContext authenticateContext = ServerWebExchangeUtils.getAuthenticateContext(exchange);
            if (null != authenticateContext) {
                AuthenticateStrategyEnum authenticateStrategy = authenticateContext.getAuthenticateStrategy();
                if (null != authenticateStrategy) {
                    securityStrategy = authenticateStrategy.toString();
                }
                span.setTag("serialNo", StringUtils.defaultIfEmpty(authenticateContext.getPlatformSerialNo(), ""));
                final AuthenticateProtocolVersion authVersionEnum = authenticateContext.getAuthenticateProtocolVersion();
                if (null != authVersionEnum) {
                    authVersion = authVersionEnum.name();
                }
            }
            span.setTag("securityStrategy", securityStrategy);
            span.setTag("authVersion", authVersion);
            Object encrypt = properties.get("encrypt");
            span.setTag("encrypt", encrypt != null && (boolean) encrypt);
            String productCode = (String) properties.get(YopConstants.PRODUCT_CODE);
            span.setTag("productCode", StringUtils.defaultIfEmpty(productCode, ""));
        }
    },
    FILE_UPLOAD("FILE_UPLOAD", "文件上传") {
        @Override
        protected void handleCommonTags(Span span, ServerWebExchange exchange) {
            String guid = "";
            if (ThreadContextUtils.contextInitialized()) {
                guid = ThreadContextUtils.getContext().getThreadUID();
            }
            span.setTag("guid", guid);
        }

        @Override
        protected void handleSpecialTags(Span span, ServerWebExchange exchange, Map<String, Object> tags) {
            super.handleSpecialTags(span, exchange, tags);
        }
    },
    BIZ_INVOKE_INFO("BIZ_INVOKE_INFO", "业务调用情况") {
        @Override
        protected void handleSpecialTags(Span span, ServerWebExchange exchange, Map<String, Object> map) {
            span.setTag("bizErrorCode", (String) map.getOrDefault("bizErrorCode", ""));
            span.setTag("bizErrorMessage", (String) map.getOrDefault("bizErrorMessage", ""));
            span.setTag("bizStatus", (String) map.get("bizStatus"));
        }
    };

    private static final Logger LOGGER = LoggerFactory.getLogger(MonitorOperationEnum.class);

    private static final Map<String, MonitorOperationEnum> VALUE_MAP = Maps.newHashMap();

    private String value;
    private String displayName;

    static {
        for (MonitorOperationEnum item : MonitorOperationEnum.values()) {
            VALUE_MAP.put(item.value, item);
        }
    }

    MonitorOperationEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static MonitorOperationEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static Map<String, MonitorOperationEnum> getValueMap() {
        return VALUE_MAP;
    }

    /**
     * 新版埋点工具的二次封装
     *
     * @param msg      消息
     * @param exchange
     */
    public void log(String msg, ServerWebExchange exchange) {
        log(msg, exchange, null);
    }

    /**
     * 新版埋点工具的二次封装
     *
     * @param msg      消息
     * @param exchange
     * @param map      需要的辅助信息
     */
    public void log(String msg, ServerWebExchange exchange, Map<String, Object> map) {
        Span span = null;
        try {
            span = MonitorLogUtils.createSpan(this.value);
            if (null != span) {
                span.log(msg);
                handleCommonTags(span, exchange);
                handleSpecialTags(span, exchange, map);
            }
        } catch (Throwable t) {
            LOGGER.warn("monitor log error." + exchange, t);
        } finally {
            try {
                // 保证日志不丢失
                MonitorLogUtils.finishSpan(span);
            } catch (Throwable t) {
                LOGGER.warn("finish monitor log error." + exchange, t);
            }
        }
    }

    /**
     * 公共的tag
     *
     * @param span
     * @param exchange
     */
    protected void handleCommonTags(Span span, ServerWebExchange exchange) {
        String requestId = "";
        String guid = "";
        String requestIp = "";
        String serverIp = "";
        String requestMethod = "";
        String appKey = "";
        String customerNo = "";
        String apiUri = "";
        String apiGroupCode = "";
        String contentType = "";
        String sdkLang = "";
        String sdkVersion = "";
        String sdkSource = "";
        String apiV2 = "";
        String sessionId = "";
        try {
            requestId = ServerWebExchangeUtils.getRequestId(exchange);
            if (ThreadContextUtils.contextInitialized()) {
                guid = ThreadContextUtils.getContext().getThreadUID();
            }
            sessionId = StringUtils.defaultString(ServerWebExchangeUtils.getSessionId(exchange));
            requestMethod = StringUtils.defaultString(ServerWebExchangeUtils.getRequestMethod(exchange));
            sdkSource = StringUtils.defaultString(ServerWebExchangeUtils.getSDKSource(exchange));

            requestIp = ServerWebExchangeUtils.getSourceIp(exchange);
            serverIp = NetworkUtils.getLocalHost();

            AppContext appContext = ServerWebExchangeUtils.getAppContext(exchange);
            if (null != appContext) {
                appKey = ServerWebExchangeUtils.getAppId(exchange);
                AppDTO app = appContext.getApp();
                customerNo = app == null ? customerNo : app.getCustomerNo();
            }
            appKey = StringUtils.isNotBlank(appKey) ? appKey : AppContext.APP_KEY_NOT_EXISTS;

            ApiContext apiContext = ServerWebExchangeUtils.getApiContext(exchange);
            if (apiContext != null) {
                apiUri = apiContext.getApiUri();
                apiGroupCode = apiContext.getApiGroup();
                ApiHandler apiHandler = apiContext.getApiHandler();
                if (null != apiHandler) {
                    apiV2 = String.valueOf(apiHandler.isApiV2());
                }
            }

            ParamContext paramContext = ServerWebExchangeUtils.getParamContext(exchange);
            if (null != paramContext) {
                contentType = paramContext.getContentType();
            }
            sdkLang = StringUtils.defaultString(ServerWebExchangeUtils.getSDKLang(exchange));
            sdkVersion = StringUtils.defaultString(ServerWebExchangeUtils.getSDKVersionString(exchange));
        } catch (Exception e) {
            LOGGER.warn("monitor log parse common tags error!", e);
        } finally {
            span.setTag("sessionId", sessionId);
            span.setTag("requestId", requestId);
            span.setTag("guid", guid);
            span.setTag("requestIp", requestIp);
            span.setTag("serverIp", serverIp);
            span.setTag("requestMethod", requestMethod);
            span.setTag("appKey", appKey);
            span.setTag("apiUri", apiUri);
            span.setTag("apiGroupCode", apiGroupCode);
            span.setTag("customerNo", customerNo);
            span.setTag("contentType", contentType);
            span.setTag("sdkLang", sdkLang);
            span.setTag("sdkVersion", sdkVersion);
            span.setTag("sdkSource", sdkSource);
            span.setTag("apiV2", apiV2);
        }
    }

    /**
     * 每种监控事件特定的tag
     *
     * @param span
     * @param exchange
     * @param tags
     */
    protected void handleSpecialTags(Span span, ServerWebExchange exchange, Map<String, Object> tags) {
        MonitorLogUtils.setTags(span, tags);
    }
}
