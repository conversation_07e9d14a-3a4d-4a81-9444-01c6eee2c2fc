/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto;

import com.yeepay.g3.yop.frame.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/2/8 5:48 下午
 */
@Getter
@Setter
public class EncryptCertificate extends BaseDTO {

    private String algorithm;

    private String nonce;

    private String associatedData;

    private String cipherText;

}
