/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.handler.entity;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/12/13 下午6:43
 */
public class FileUploadResult implements Serializable {

    private static final long serialVersionUID = -1;

    private String fileDir;

    private String fileName;

    private String fileExt;

    private String fileLocation;

    private Throwable throwable;

    private FileUploadResult(Builder builder) {
        setFileDir(builder.fileDir);
        setFileName(builder.fileName);
        setFileExt(builder.fileExt);
        setFileLocation(builder.fileLocation);
        setThrowable(builder.throwable);
    }

    public String getFileDir() {
        return fileDir;
    }

    public void setFileDir(String fileDir) {
        this.fileDir = fileDir;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileExt() {
        return fileExt;
    }

    public void setFileExt(String fileExt) {
        this.fileExt = fileExt;
    }

    public String getFileLocation() {
        return fileLocation;
    }

    public void setFileLocation(String fileLocation) {
        this.fileLocation = fileLocation;
    }

    public Throwable getThrowable() {
        return throwable;
    }

    public void setThrowable(Throwable throwable) {
        this.throwable = throwable;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    @Override
    public String toString() {
        try {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
        } catch (Exception e) {
            return super.toString();
        }
    }

    public static final class Builder {

        private String fileDir;

        private String fileName;

        private String fileExt;

        private String fileLocation;

        private Throwable throwable;

        private Builder() {
        }

        public Builder fileDir(String fileDir) {
            this.fileDir = fileDir;
            return this;
        }

        public Builder fileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        public Builder fileExt(String fileExt) {
            this.fileExt = fileExt;
            return this;
        }

        public Builder fileLocation(String fileLocation) {
            this.fileLocation = fileLocation;
            return this;
        }

        public Builder throwable(Throwable throwable) {
            this.throwable = throwable;
            return this;
        }

        public FileUploadResult build() {
            return new FileUploadResult(this);
        }

    }
}
