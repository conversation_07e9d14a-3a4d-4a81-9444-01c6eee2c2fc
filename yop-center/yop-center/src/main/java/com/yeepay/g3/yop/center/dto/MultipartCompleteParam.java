/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/16 下午3:59
 */
public class MultipartCompleteParam extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private String bucket;

    private String key;

    private String uploadId;

    private List<Part> parts;

    @JsonIgnore
    public String getBucket() {
        return bucket;
    }

    @JsonSetter("bucket")
    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getUploadId() {
        return uploadId;
    }

    public void setUploadId(String uploadId) {
        this.uploadId = uploadId;
    }

    @JsonGetter("partETags")
    public List<Part> getParts() {
        return parts;
    }

    @JsonSetter("parts")
    public void setParts(List<Part> parts) {
        this.parts = parts;
    }
}
