package com.yeepay.g3.yop.center.handler.impl;

import com.baiwang.bop.client.IBopClient;
import com.baiwang.bop.client.impl.BopRestClient;
import com.baiwang.bop.request.impl.invoice.impl.InvoiceOpenRequest;
import com.baiwang.bop.respose.entity.InvoiceOpenResponse;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.AbstractBwCloudAPIHandler;
import com.yeepay.g3.yop.center.handler.entity.BWCloudResult;
import com.yeepay.g3.yop.center.handler.entity.BwConfig;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: BwCloudIssueAPIHandler<br/>
 * description: 百望云开发票接口<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年9月21日 上午11:22:07
 */
@Component
public class BwCloudIssueAPIHandler extends AbstractBwCloudAPIHandler<InvoiceOpenResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BwCloudIssueAPIHandler.class);

    @Override
    protected BWCloudResult<InvoiceOpenResponse> execute(BwConfig bwConfig, String token, Map<String, Object> params) {
        String serviceUrl = bwConfig.getServerUrl();
        String appKey = bwConfig.getAppKey();
        String appSecret = bwConfig.getAppSecret();
        BWCloudResult<InvoiceOpenResponse> result = new BWCloudResult<InvoiceOpenResponse>();
        IBopClient client = new BopRestClient(serviceUrl, appKey, appSecret);
        InvoiceOpenRequest request = (InvoiceOpenRequest) params.get("invoiceOpenRequest");
        InvoiceOpenResponse response = client.execute(request, token, InvoiceOpenResponse.class);
        result.setReturnCode(RES_SUCCESS);
        result.setReturnMessages("响应成功");
        result.setReturnData(response);
        return result;
    }

}
