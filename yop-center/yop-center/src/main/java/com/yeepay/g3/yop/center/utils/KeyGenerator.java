/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.utils;


import com.yeepay.boot.components.idgen.sequence.SnowflakeIdWorker;
import com.yeepay.boot.components.utils.Encodes;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/1/7 下午4:32
 */
public class KeyGenerator {

    private static final SnowflakeIdWorker idWorker = new SnowflakeIdWorker();

    public static String generateKey() {
        return Encodes.encodeBase62(idWorker.next());
    }
}
