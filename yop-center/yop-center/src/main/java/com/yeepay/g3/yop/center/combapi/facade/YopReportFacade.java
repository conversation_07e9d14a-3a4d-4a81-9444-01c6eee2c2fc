/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi.facade;

import com.yeepay.g3.yop.center.dto.report.YopReportRequest;
import com.yeepay.g3.yop.center.dto.report.YopReportResponse;

/**
 * title: 客户端上报<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/6
 */
public interface YopReportFacade {

    YopReportResponse report(YopReportRequest request);
}
