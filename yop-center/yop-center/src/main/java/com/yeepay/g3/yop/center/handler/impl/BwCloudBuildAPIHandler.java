package com.yeepay.g3.yop.center.handler.impl;

import com.baiwang.bop.client.IBopClient;
import com.baiwang.bop.client.impl.BopRestClient;
import com.baiwang.bop.request.impl.invoice.impl.FormatfileBuildRequest;
import com.baiwang.bop.respose.entity.FormatfileBuildResponse;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.AbstractBwCloudAPIHandler;
import com.yeepay.g3.yop.center.handler.entity.BWCloudResult;
import com.yeepay.g3.yop.center.handler.entity.BwConfig;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: BwCloudUploadAPIHandler<br/>
 * description: 百望云版式文件生成接口<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年10月24日 下午7:47:29
 */
@Component
public class BwCloudBuildAPIHandler extends AbstractBwCloudAPIHandler<FormatfileBuildResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BwCloudBuildAPIHandler.class);

    @Override
    protected BWCloudResult<FormatfileBuildResponse> execute(BwConfig bwConfig, String token, Map<String, Object> params) {
        String serviceUrl = bwConfig.getServerUrl();
        String appKey = bwConfig.getAppKey();
        String appSecret = bwConfig.getAppSecret();
        BWCloudResult<FormatfileBuildResponse> result = new BWCloudResult<FormatfileBuildResponse>();
        IBopClient client = new BopRestClient(serviceUrl, appKey, appSecret);
        FormatfileBuildRequest request = (FormatfileBuildRequest) params.get("formatfileBuildRequest");
        FormatfileBuildResponse response = client.execute(request, token, FormatfileBuildResponse.class);
        result.setReturnCode(RES_SUCCESS);
        result.setReturnMessages("响应成功");
        result.setReturnData(response);
        return result;
    }

}
