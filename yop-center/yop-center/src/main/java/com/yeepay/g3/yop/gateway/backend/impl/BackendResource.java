/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.yop.gateway.backend.impl;

import com.yeepay.g3.yop.frame.classloader.SmartClassLoader;
import com.yeepay.g3.yop.frame.classloader.Versioned;
import com.yeepay.g3.yop.frame.rmi.ReferenceConfigCache;
import com.yeepay.g3.yop.gateway.backend.ClassLoadMode;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 4:47 PM
 */
public class BackendResource {

    private final ClassLoader classLoader;

    private final long version;

    private final ClassLoadMode classLoadMode;

    private final ReferenceConfigCache referenceConfigCache;

    public BackendResource(String backendAppName, ClassLoader classLoader) {
        this.classLoader = classLoader;
        if (classLoader instanceof Versioned) {
            this.version = ((Versioned) classLoader).getVersion();
        } else {
            this.version = 0;
        }
        if (classLoader instanceof SmartClassLoader || classLoader == null) {
            this.classLoadMode = ClassLoadMode.REMOTE;
        } else {
            this.classLoadMode = ClassLoadMode.LOCAL;
        }
        this.referenceConfigCache = new ReferenceConfigCache(backendAppName + "_" + version);
    }

    public ClassLoader getClassLoader() {
        return classLoader;
    }

    public long getVersion() {
        return version;
    }

    public ClassLoadMode getClassLoadMode() {
        return classLoadMode;
    }

    public ReferenceConfigCache getReferenceConfigCache() {
        return referenceConfigCache;
    }

    public void destroy() {
        this.referenceConfigCache.destroyAll();
    }

}
