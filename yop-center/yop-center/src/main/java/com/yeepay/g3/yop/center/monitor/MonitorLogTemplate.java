package com.yeepay.g3.yop.center.monitor;

/**
 * title: 监控日志模版<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/12/8 上午11:29
 */
public class MonitorLogTemplate {

    private static final String MONITOR_LOG_PREFIX = "[monitor],eventType:";

    /*------------event start-----------------------------*/

    private static final String REQUEST_EVENT = "request";//接收请求事件，做统计基数
    private static final String DEPRECATED_AUTHORIZATION_EVENT = "deprecated_authorization";//老版认证（却少请求头）事件，判断是否能全面迁移
    private static final String LOAD_API_SUCCESS_EVENT = "load_api_success";//暴漏api成功事件
    private static final String LOAD_API_FAILURE_EVENT = "load_api_failure";//暴漏api失败事件
    private static final String AUTHENTICATE_EVENT = "authenticate";//开始认证事件

    /*------------event end-----------------------------*/


    /*------------template start(used for business <NAME_EMAIL>)*/

    public static final String ACCEPT_REQUEST = MONITOR_LOG_PREFIX + REQUEST_EVENT + ",type:req" + ",{}";//接受请求
    public static final String REQUEST_SUCCESS = MONITOR_LOG_PREFIX + REQUEST_EVENT + ",type:res,status:success,{},timeElapse:{}ms";//请求成功
    public static final String REQUEST_FAILURE = MONITOR_LOG_PREFIX + REQUEST_EVENT + ",type:res,status:failure,{},timeElapse:{}ms,error_code:{},sub_error_code:{},error_message:{}";//请求失败

    public static final String END_AUTHENTICATE_SUCCESS = MONITOR_LOG_PREFIX + AUTHENTICATE_EVENT + ",type:res,status:success,{},timeElapse:{}ms";//认证成功
    public static final String END_AUTHENTICATE_FAILURE = MONITOR_LOG_PREFIX + AUTHENTICATE_EVENT + ",type:res,status:failure,{},timeElapse:{}ms";//认证失败

    /*------------template end(used for business <NAME_EMAIL>)*/


    /*------------template start------------------------*/

    public static final String DEPRECATED_AUTHORIZATION = MONITOR_LOG_PREFIX + DEPRECATED_AUTHORIZATION_EVENT + ",requestId:{},appKey:{},apiUri:{}";//不推荐（老版）的认证方式
    public static final String LOAD_API_SUCCESS = MONITOR_LOG_PREFIX + LOAD_API_SUCCESS_EVENT + ",apiUri:{}";//暴漏api成功
    public static final String LOAD_API_FAILURE = MONITOR_LOG_PREFIX + LOAD_API_FAILURE_EVENT + ",apiUri:{}";//暴漏api失败

    /*------------template end--------------------------*/
}
