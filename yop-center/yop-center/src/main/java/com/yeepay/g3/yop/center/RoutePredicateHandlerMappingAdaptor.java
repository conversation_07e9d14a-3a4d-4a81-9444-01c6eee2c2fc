/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center;

import com.google.common.collect.Maps;
import com.yeepay.g3.yop.center.monitor.MonitorLogTemplate;
import com.yeepay.g3.yop.center.monitor.enumtype.MonitorOperationEnum;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.exception.service.SystemServiceUnavailableException;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.ext.gateway.context.impl.InvokeProcessContext;;
import com.yeepay.g3.yop.ext.gateway.filter.GlobalFilter;
import com.yeepay.g3.yop.gateway.handler.RoutePredicateHandlerMapping;
import com.yeepay.g3.yop.gateway.handler.WebHandler;
import com.yeepay.g3.yop.gateway.server.DefaultServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.GATEWAY_STARTED_TIME_ATTR;
import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.GATE_INVOKE_PROCESS_CONTEXT_ATTR;

/**
 * title: RoutePredicateHandlerMapping 适配器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/2 21:23
 */
@Slf4j
public class RoutePredicateHandlerMappingAdaptor {

    private RoutePredicateHandlerMapping routePredicateHandlerMapping;

    // 兜底异常处理
    private GlobalFilter responseFilter;

    /**
     * 处理
     *
     * @param req 请求
     * @param res 响应
     * @return true 走原逻辑
     */
    public boolean service(HttpServletRequest req, HttpServletResponse res) {
        long startTime = System.currentTimeMillis();
        log.info("new invoke chain is running,apiUri:{}", StringUtils.replace(req.getRequestURI(), req.getContextPath(), ""));

        try {
            ServerWebExchange exchange = new DefaultServerWebExchange(req, res);
            ServerWebExchangeUtils.getProperties(exchange).put("startTime", startTime);
            ServerWebExchangeUtils.getInvokeProcessContext(exchange).setStartTime(startTime);
            /*接受请求，打印接受请求日志，便于日志分析，视为认证基数（统计各种指标时，以此为分母）*/
            log.info(MonitorLogTemplate.ACCEPT_REQUEST, exchange);
            MonitorOperationEnum.REQUEST.log("", exchange);
            exchange.getAttributes().put(GATEWAY_STARTED_TIME_ATTR, startTime);
            WebHandler webHandler = routePredicateHandlerMapping.getHandlerInternal(exchange);
            Map<String, Object> map = Maps.newHashMap();
            // TODO 丑陋的代码
            if (null == webHandler) {
                log.info("switch to old logic");
                return true;
            } else {
                webHandler.handle(exchange);

                Long endTime = (Long) ServerWebExchangeUtils.getProperties(exchange).get("endTime");
                if (null == endTime) {
                    log.error("endTime is null, use System.currentTimeMillis()");
                    endTime = System.currentTimeMillis();
                }
                if (ServerWebExchangeUtils.internalExceptionHappened(exchange)) {
                    InternalException internalException = ServerWebExchangeUtils.getInvokeProcessContext(exchange).getInternalException();
                    map.put("exception", internalException);
                    /*请求失败，打印失败事件日志，并打印简要消息(包含异常)，便于日志分析*/
                    log.info(MonitorLogTemplate.REQUEST_FAILURE, exchange, System.currentTimeMillis() - startTime, internalException.getStatus().getCode(), internalException.getCode(), internalException.getMessage());
                } else {
                    /*请求成功，打印成功事件日志，并打印简要消息，便于日志分析*/
                    log.info(MonitorLogTemplate.REQUEST_SUCCESS, exchange, System.currentTimeMillis() - startTime);
                }
                map.put("totalLatency", endTime - startTime);
                MonitorOperationEnum.RESPONSE.log("", exchange, map);
                return false;
            }
        } catch (Throwable throwable) {
            /*理论上是不会走到这里的，但总有例外，为了不吞异常，在此打印一下*/
            log.error("unexpected error happen when process request,", throwable);
            InternalException internalException = new SystemServiceUnavailableException();
            Response response = new Response(internalException);
            InvokeProcessContext invokeProcessContext = new InvokeProcessContext();
            invokeProcessContext.setResponse(response);
            invokeProcessContext.setInternalException(internalException);
            ServerWebExchange exchange = new DefaultServerWebExchange(req, res, true);
            exchange.getAttributes().put(GATE_INVOKE_PROCESS_CONTEXT_ATTR, invokeProcessContext);
            responseFilter.filter(exchange);
            return false;
        }

    }

    public void setRoutePredicateHandlerMapping(RoutePredicateHandlerMapping routePredicateHandlerMapping) {
        this.routePredicateHandlerMapping = routePredicateHandlerMapping;
    }

    public void setResponseFilter(GlobalFilter responseFilter) {
        this.responseFilter = responseFilter;
    }
}
