/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto.report;

import lombok.Data;

import java.io.Serializable;

/**
 * title: 上报内容-YOP域名请求<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/3/20
 */
@Data
public class YopReport implements Serializable {

    private static final long serialVersionUID = -1L;

    private String type;

    private int version;

    private Object payload;

    private String beginDate;

    private String endDate;
}
