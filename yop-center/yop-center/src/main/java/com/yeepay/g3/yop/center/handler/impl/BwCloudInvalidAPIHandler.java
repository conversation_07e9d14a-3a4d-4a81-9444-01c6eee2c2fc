package com.yeepay.g3.yop.center.handler.impl;

import com.baiwang.bop.client.IBopClient;
import com.baiwang.bop.client.impl.BopRestClient;
import com.baiwang.bop.request.impl.invoice.impl.InvoiceInvalidRequest;
import com.baiwang.bop.respose.entity.InvoiceInvalidResponse;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.AbstractBwCloudAPIHandler;
import com.yeepay.g3.yop.center.handler.entity.BWCloudResult;
import com.yeepay.g3.yop.center.handler.entity.BwConfig;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: BwCloudInvalidAPIHandler<br/>
 * description: 百望云发票作废接口<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年10月25日 下午2:21:40
 */
@Component
public class BwCloudInvalidAPIHandler extends AbstractBwCloudAPIHandler<InvoiceInvalidResponse> {

    @Override
    protected BWCloudResult<InvoiceInvalidResponse> execute(BwConfig bwConfig, String token, Map<String, Object> params) {
        String serviceUrl = bwConfig.getServerUrl();
        String appKey = bwConfig.getAppKey();
        String appSecret = bwConfig.getAppSecret();
        BWCloudResult<InvoiceInvalidResponse> result = new BWCloudResult<InvoiceInvalidResponse>();
        IBopClient client = new BopRestClient(serviceUrl, appKey, appSecret);
        InvoiceInvalidRequest request = (InvoiceInvalidRequest) params.get("invoiceInvalidRequest");
        InvoiceInvalidResponse response = client.execute(request, token, InvoiceInvalidResponse.class);
        result.setReturnCode(RES_SUCCESS);
        result.setReturnMessages("响应成功");
        result.setReturnData(response);
        return result;
    }

}
