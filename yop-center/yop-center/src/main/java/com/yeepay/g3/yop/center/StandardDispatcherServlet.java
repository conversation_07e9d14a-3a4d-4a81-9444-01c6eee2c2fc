package com.yeepay.g3.yop.center;

import com.caucho.hessian.client.HessianGlobalHeaders;
import com.google.common.collect.Maps;
import com.yeepay.g3.facade.yop.sys.dto.AppDTO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.client.hessian.HessianProxyFactoryWrapper;
import com.yeepay.g3.yop.center.utils.InvokeSwitchUtils;
import com.yeepay.g3.yop.frame.cache.AppAliasRefreshableLocalCache;
import com.yeepay.g3.yop.frame.cache.AppRefreshableLocalCache;
import com.yeepay.g3.yop.frame.chain.InterceptorChain;
import com.yeepay.g3.yop.frame.chain.StandardChainExecutionControl;
import com.yeepay.g3.yop.frame.context.AuthenticateContext;
import com.yeepay.g3.yop.frame.context.factory.AuthenticateContextFactory;
import com.yeepay.g3.yop.frame.context.impl.StandardContext;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.exception.service.SystemServiceUnavailableException;
import com.yeepay.g3.yop.frame.http.Headers;
import com.yeepay.g3.yop.frame.monitor.MonitorLogTemplate;
import com.yeepay.g3.yop.frame.monitor.enumtype.MonitorOperationEnum;
import com.yeepay.g3.yop.frame.protocol.ProtocolAnalyzer;
import com.yeepay.g3.yop.frame.protocol.ProtocolVersion;
import com.yeepay.g3.yop.frame.protocol.ResponseMarshaller;
import com.yeepay.g3.yop.frame.protocol.ResponseMarshallerFactory;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import com.yeepay.g3.yop.frame.xsd.SystemParamNames;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.support.SpringBeanAutowiringSupport;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * title: yop入口servlet<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/10/25 下午5:41
 */
public class StandardDispatcherServlet extends HttpServlet {

    private static final long serialVersionUID = -19910118L;

    private static final Logger LOGGER = LoggerFactory.getLogger(StandardDispatcherServlet.class);

    private RoutePredicateHandlerMappingAdaptor routePredicateHandlerMappingAdaptor;

    @Autowired
    private InterceptorChain standardInterceptorChain;

    private static final String APP_KEY_HEADER = "X-YOP-AppKey";

    @Autowired
    private AppAliasRefreshableLocalCache appAliasRefreshableLocalCache;

    @Autowired
    private AppRefreshableLocalCache appLocalCache;

    @Override
    public void init(ServletConfig config) throws ServletException {
        // 让 Servlet 支持 spring bean autowiring
        SpringBeanAutowiringSupport.processInjectionBasedOnServletContext(this, config.getServletContext());

        HessianGlobalHeaders.addHeader(Headers.YOP_REQUEST_SOURCE, "yop-center");
        HessianProxyFactoryWrapper.DEFAULT_CONNECT_TIMEOUT = ConfigUtils.loadLongValue(ConfigEnum.YOP_CENTER_RMI_CONNECT_TIMEOUT);
    }

    @Override
    protected void service(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException {
        boolean enableNewChain = enableFilterChain(req);
        String protocolPrefix = getSecurityDef(req);
        LOGGER.info("protocolPrefix {} new chain {}", protocolPrefix, enableNewChain ? "on" : "off");
        if (!enableNewChain || routePredicateHandlerMappingAdaptor.service(req, res)) {
            doService(req, res);
        }
    }

    protected void doService(HttpServletRequest req, HttpServletResponse res) throws IOException {
        LOGGER.info("old invoke chain is running,apiUri:{}", StringUtils.replace(req.getRequestURI(), req.getContextPath(), ""));
        ProtocolVersion protocolVersion = ProtocolAnalyzer.analysis(req);
        Response response = null;
        StandardContext context = null;
        Map<String, Object> map = Maps.newHashMap();
        long startTime = System.currentTimeMillis();
        try {
            context = new StandardContext(req, res);
            // 统一startTime
            context.getProperties().put("startTime", startTime);
            /*接受请求，打印接受请求日志，便于日志分析，视为认证基数（统计各种指标时，以此为分母）*/
            LOGGER.info(MonitorLogTemplate.ACCEPT_REQUEST, context);
            MonitorOperationEnum.REQUEST.log("", context);
            AuthenticateContext authenticateContext = AuthenticateContextFactory.createAuthenticateContext(context);
            context.setAuthenticateContext(authenticateContext);
            response = standardInterceptorChain.getHead().intercept(context, new StandardChainExecutionControl(standardInterceptorChain, context));
        } catch (InternalException e) {
            /*请求失败，打印失败事件日志，并打印简要消息(包含异常)，便于日志分析*/
            LOGGER.info(MonitorLogTemplate.REQUEST_FAILURE, context, System.currentTimeMillis() - startTime, e.getStatus().getCode(), e.getCode(), e.getMessage());
            response = new Response(e);
            map.put("exception", e);
            return;
        } catch (Throwable e) {
            /*如果程序足够完美，是不会走到这里的，所有的异常都会转为InternalException被上面捕捉;但总有例外，为了不吞异常，在此打印一下*/
            LOGGER.error("unexpected error happen when process request," + context, e);
            response = new Response(new SystemServiceUnavailableException());
            return;
        } finally {
            ResponseMarshaller responseMarshaller = ResponseMarshallerFactory.getResponseMarshaller(protocolVersion);
            responseMarshaller.marshal(context, response, res);
            res.getOutputStream().flush();
            /*秉承谁打开谁负责的原则，这里不应该去close */
            /*res.getOutputStream().close();*/
            Long endTime = (Long) context.getProperties().get("endTime");
            if (null == endTime) {
                LOGGER.error("endTime is null, use System.currentTimeMillis()");
                endTime = System.currentTimeMillis();
            }
            map.put("totalLatency", endTime - startTime);
            MonitorOperationEnum.RESPONSE.log("", context, map);
        }
        /*请求成功，打印成功事件日志，并打印简要消息，便于日志分析*/
        LOGGER.info(MonitorLogTemplate.REQUEST_SUCCESS, context, System.currentTimeMillis() - startTime);
    }

    private boolean enableFilterChain(HttpServletRequest request) {
        List<String> enableNewInvokeChainSecurityPrefix = ConfigUtils.getSysConfigParam(ConfigEnum.ENABLE_NEW_INVOKE_CHAIN_SECURITY_PREFIX, List.class);
        if (!enableNewInvokeChainSecurityPrefix.contains(getSecurityDef(request))) {
            return false;
        }
        Map<String, Long> config = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_INVOKE_CHAIN_THRESHOLD, Map.class);

        String appKey = getAppKey(request);
        String customerNo = null;
        if (StringUtils.isNotEmpty(appKey)) {
            appKey = StringUtils.defaultIfEmpty(appAliasRefreshableLocalCache.get(appKey), appKey);
            AppDTO app = appLocalCache.get(appKey);
            if (!Objects.isNull(app)) {
                customerNo = app.getCustomerNo();
            }
        }
        String apiUri = StringUtils.replace(request.getRequestURI(), request.getContextPath(), "");
        return InvokeSwitchUtils.enableFilterChain(customerNo, apiUri, config);

    }

    private String getSecurityDef(HttpServletRequest request) {
        String authHeader = request.getHeader(Headers.AUTHORIZATION);
        // 所有的旧版都走都aes128,为了区分正常协议给个特殊标识
        String defaultSecurityDef = "DeprecatedHmac";

        if (StringUtils.isEmpty(authHeader)) {
            LOGGER.warn("authHeader is empty");
            return defaultSecurityDef;
        }
        return StringUtils.defaultIfEmpty(StringUtils.substringBefore(authHeader, " "), defaultSecurityDef);
    }

    private String getAppKey(HttpServletRequest request) {
        // 从header中传递的appKey
        String appKey = request.getHeader(APP_KEY_HEADER);
        if (StringUtils.isNotBlank(appKey)) {
            return appKey;
        }
        // 从参数中传递的appKey
        return request.getParameter(SystemParamNames.APP_KEY);

    }

    @Autowired
    public void setRoutePredicateHandlerMappingAdaptor(RoutePredicateHandlerMappingAdaptor routePredicateHandlerMappingAdaptor) {
        this.routePredicateHandlerMappingAdaptor = routePredicateHandlerMappingAdaptor;
    }
}
