/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto.report;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * title: 上报请求<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/13
 */
@Data
public class YopReportRequest implements Serializable {

    private static final long serialVersionUID = -1L;

    private String sessionId;

    private String requestId;

    private String appKey;

    private String customerNo;

    private String sdkLang;

    private String sdkVersion;

    private List<YopReport> reports;

}
