/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

@Component
public class MockFacadeProxy {

    private static final Logger LOGGER = LoggerFactory.getLogger(MockFacadeProxy.class);

    public String mockBackendLatency(@RequestParam(value = "backendLatency", defaultValue = "20000") Long backendLatency) throws InterruptedException {
        Thread.sleep(backendLatency);
        return "success";
    }

    public void testReturnVoid() throws InterruptedException {
        LOGGER.warn("invoke return void");
    }

    public String testReturnString() throws InterruptedException {
        LOGGER.warn("invoke return a string");
        return "Hello World!";
    }

}
