/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.yop.gateway.backend.impl;

import com.alibaba.dubbo.config.utils.ReferenceConfigCache;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.yeepay.boot.components.utils.Exceptions;
import com.yeepay.g3.facade.yop.api.enums.EndProtocolEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemotingProtocol;
import com.yeepay.g3.yop.frame.YopClassLoader;
import com.yeepay.g3.yop.frame.classloader.SmartClassLoader;
import com.yeepay.g3.yop.frame.definition.ApiDefinition;
import com.yeepay.g3.yop.frame.exception.service.backend.BackendInitializationException;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import com.yeepay.g3.yop.ext.gateway.backend.ApiBackend;
import com.yeepay.g3.yop.gateway.backend.ClassLoadMode;
import com.yeepay.g3.yop.gateway.backend.RmiBackend;
import com.yeepay.g3.yop.gateway.backend.ScalableBackendApp;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 4:42 PM
 */
public abstract class RemoteBackendApp<SCL extends SmartClassLoader> implements ScalableBackendApp {

    private static final Logger LOGGER = LoggerFactory.getLogger(RemoteBackendApp.class);

    private final ConcurrentHashMap<String, Object> parallelRestartLockMap = new ConcurrentHashMap<>();

    protected final String backendName;

    protected final Set<String> endClasses;

    private volatile BackendResource resource;

    /**
     * 延迟销毁任务队列，后台定时执行
     */
    private final DelayQueue<BackendResourceDelayReleaseTask> recycledResources;

    public RemoteBackendApp(String backendName, String endClass) {
        Preconditions.checkArgument(StringUtils.isNotBlank(backendName), "backendApp can not be empty.");
        Preconditions.checkArgument(StringUtils.isNotBlank(endClass), "backendApp:%s, endClass can not be empty.", backendName);
        this.backendName = backendName;
        this.endClasses = Sets.newLinkedHashSet();
        rememberEndClass(endClass);
        this.recycledResources = new DelayQueue<>();
    }

    @Override
    public String getName() {
        return backendName;
    }

    @Override
    public ClassLoader getClassLoader() {
        return resource.getClassLoader();
    }

    @Override
    public ApiBackend createApiBackend(ApiDefinition apiDef) throws Exception {
        return createDegrandableBackend(apiDef);
    }

    private ApiBackend createDegrandableBackend(ApiDefinition apiDef) throws Exception {
        BackendResource resource = getResource();
        ClassLoader backendAppClassLoader = resource.getClassLoader();

        // TODO 是否真的需要更换 classLoader？
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(backendAppClassLoader);

        RmiBackend rmiBackend = new RmiBackend();
        try {
            String endClass = apiDef.getEndClass();
            Class<?> serviceClass = backendAppClassLoader.loadClass(endClass);
            Method endMethod = YopClassLoader.getMethod(serviceClass, apiDef.getEndMethod());
            rmiBackend.setEndMethod(endMethod);
            RemotingProtocol remotingProtocol = EndProtocolEnum.HESSIAN == apiDef.getEndProtocol() ? RemotingProtocol.HESSIAN : RemotingProtocol.HTTPINVOKER;
            Object proxy = resource.getReferenceConfigCache().get(new com.yeepay.g3.yop.frame.rmi.ReferenceConfig(remotingProtocol, serviceClass, apiDef.getEndServiceUrl()));
            rmiBackend.setProxy(proxy);
        } catch (ClassNotFoundException e) {
            Throwable root = Exceptions.getRootCause(e);
            throw new BackendInitializationException("Class not found, detail:{0}", root, Exceptions.getMessage(root));
        } catch (Exception e) {
            Throwable root = Exceptions.getRootCause(e);
            throw new BackendInitializationException(root);
        } finally {
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
        return rmiBackend;
    }

    @Override
    public void init(String version) {
        // 防止并发升级
        synchronized (getRemoteBackendAppRestartLock(backendName)) {
            BackendResource currentResource = getResource();
            ClassLoadMode specifiedClassLoadModel = getClassLoadMode();
            ClassLoadMode currentClassLoadMode = currentResource == null ? null : currentResource.getClassLoadMode();
            ClassLoadMode targetClassLoadMode = specifiedClassLoadModel == null ? currentClassLoadMode : specifiedClassLoadModel;
            LOGGER.debug("update backendApp start, backendApp:{}, version:{}, loadModeInfo[current:{},specified:{},target:{}].",
                    backendName, currentResource == null ? null : currentResource.getVersion(),
                    currentClassLoadMode, specifiedClassLoadModel, targetClassLoadMode);

            ClassLoader classLoader = findClassLoader(targetClassLoadMode, version);
            if (null == classLoader) {
                LOGGER.warn("update backendApp failure, backendApp:{}, classLoadMode:{}.",
                        backendName, targetClassLoadMode);
                return;
            }

            setResource(new BackendResource(backendName, classLoader));
            LOGGER.info("update backendApp success, backendApp:{}, classLoadMode:{}, version:{},",
                    backendName, resource.getClassLoadMode(), resource.getVersion());
            if (currentResource != null) {
                recycledResources.offer(new BackendResourceDelayReleaseTask(currentResource));
            }
        }
    }

    private ClassLoader findClassLoader(ClassLoadMode classLoadMode, String version) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (ClassLoadMode.REMOTE.equals(classLoadMode)) {
            classLoader = generateRemoteClassLoader(backendName, version, classLoader);
        } else if (null == classLoadMode) {
            try {
                tryContextClassLoader(classLoader);
                LOGGER.info("loadModel need specified LOCAL, backendApp:{}.", backendName);
            } catch (ClassNotFoundException e) {
                classLoader = generateRemoteClassLoader(backendName, version, classLoader);
                LOGGER.info("loadModel need specified REMOTE, backendApp:{}.", backendName);
            }
        }
        return classLoader;
    }

    private void tryContextClassLoader(final ClassLoader classLoader) throws ClassNotFoundException {
        long maxRetry = ConfigUtils.loadIntValue(ConfigEnum.YOP_CENTER_LOCAL_CLASSLOADER_MAX_RETRY);
        Iterator<String> endClassIterator = endClasses.iterator();
        int retryCount = 0;
        Class<?> clazz = null;
        while (endClassIterator.hasNext() && retryCount++ < maxRetry && null == clazz) {
            String endClass = endClassIterator.next();
            try {
                clazz = classLoader.loadClass(endClass);
            } catch (Throwable e) {
                if (!(e instanceof ClassNotFoundException)) {
                    LOGGER.warn("failed to load endClass:" + endClass, e);
                }
                if (retryCount >= maxRetry) {
                    throw e;
                }
            }
        }
        if (null == clazz) {
            throw new ClassNotFoundException();
        }
    }

    private SCL generateRemoteClassLoader(String backendApp, String version, ClassLoader parentClassLoader) {
        SCL classLoader = null;
        try {
            classLoader = doGenerateRemoteClassLoader(backendApp, version, parentClassLoader);
        } catch (Throwable e) {
            LOGGER.error("", e);
        }
        return classLoader;
    }

    protected abstract SCL doGenerateRemoteClassLoader(String backendApp, String version, ClassLoader parentClassLoader);

    @Override
    public final boolean rememberEndClass(String endClass) {
        return endClasses.add(endClass);
    }

    private ClassLoadMode getClassLoadMode() {
        ClassLoadMode classLoadMode = null;
        try {
            Map<String, String> loadModelConfigs = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_BACKEND_REMOTE_LOAD_SWITCH);
            String loadModeStr = loadModelConfigs.get(backendName);
            if (StringUtils.isNotEmpty(loadModeStr)) {
                classLoadMode = ClassLoadMode.valueOf(loadModeStr);
            }
        } catch (Throwable e) {
            LOGGER.warn("", e);
        }
        return classLoadMode;
    }

    @Override
    public void backGroundProgress() {
        //最多释放四次资源（防止耗时过长）
        for (int i = 0; i < 4; i++) {
            //响应后台任务的关闭
            if (Thread.currentThread().isInterrupted()) {
                return;
            }
            BackendResourceDelayReleaseTask task = recycledResources.poll();
            if (task == null) {
                break;
            }
            try {
                task.realeseResource();
                LOGGER.info("resource destory success,backendApp:{},version:{}.", backendName, task.resource.getVersion());
            } catch (Throwable ex) {
                LOGGER.error("resource destroy failure,backendApp:" + backendName + ",version:" + task.resource.getVersion(), ex);
            }
        }
    }

    @Override
    public final void close() {
        destroyPrevVersion("_");
        Collection<BackendResourceDelayReleaseTask> delayReleaseTasks = new ArrayList<>(recycledResources.size());
        recycledResources.drainTo(delayReleaseTasks);
        for (BackendResourceDelayReleaseTask task : delayReleaseTasks) {
            try {
                task.realeseResource();
            } catch (Throwable ex) {
                LOGGER.error("resource destroy failure, backendApp:" + backendName + ",version:" + resource.getVersion(), ex);
            }
        }
    }


    @Override
    public void destroyPrevVersion() {
        String version = Long.toString(getResource().getVersion());
        destroyPrevVersion(version);
    }

    private void destroyPrevVersion(String version) {
        Set<String> versions = ReferenceConfigCache.getKeys();
        for (String ver : versions) {
            if (StringUtils.startsWith(ver, backendName)) {
                if (StringUtils.compare(ver, version) >= 0) {
                    continue;
                }
                ReferenceConfigCache.getCache(ver).destroyAll();
            }
        }
    }

    private Object getRemoteBackendAppRestartLock(String className) {
        Object lock = this;
        if (parallelRestartLockMap != null) {
            Object newLock = new Object();
            lock = parallelRestartLockMap.putIfAbsent(className, newLock);
            if (lock == null) {
                lock = newLock;
            }
        }
        return lock;
    }

    protected BackendResource getResource() {
        return resource;
    }

    private void setResource(BackendResource resource) {
        this.resource = resource;
    }

    private class BackendResourceDelayReleaseTask implements Delayed {

        private final long releaseTime;

        private final BackendResource resource;

        public BackendResourceDelayReleaseTask(BackendResource resource) {
            this.resource = resource;
            this.releaseTime = System.currentTimeMillis() + 60 * 1000;
        }

        public long getReleaseTime() {
            return releaseTime;
        }

        void realeseResource() {
            resource.destroy();
        }

        @Override
        public long getDelay(TimeUnit unit) {
            return unit.convert(this.releaseTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed o) {
            return (int) (this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS));
        }
    }

}
