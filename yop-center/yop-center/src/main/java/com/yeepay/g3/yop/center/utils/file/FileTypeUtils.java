package com.yeepay.g3.yop.center.utils.file;

import com.yeepay.g3.facade.yop.monitor.enums.FileTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * title: 文件类型<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/4/5 09:43
 */
public final class FileTypeUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileTypeUtils.class);

    public static final Map<String, FileTypeEnum> FILE_TYPE_MAP = new HashMap<String, FileTypeEnum>();

    private FileTypeUtils() {
    }

    static {
        getAllFileType(); //初始化文件类型信息
    }

    private static void getAllFileType() {
        FILE_TYPE_MAP.put("ffd8ffe000104a464946", FileTypeEnum.IMAGE); //JPEG (jpg)
        FILE_TYPE_MAP.put("89504e470d0a1a0a0000", FileTypeEnum.IMAGE); //PNG (png)
        FILE_TYPE_MAP.put("47494638396126026f01", FileTypeEnum.IMAGE); //GIF (gif)
        FILE_TYPE_MAP.put("49492a00227105008037", FileTypeEnum.IMAGE); //TIFF (tif)
        FILE_TYPE_MAP.put("424d228c010000000000", FileTypeEnum.IMAGE); //16色位图(bmp)
        FILE_TYPE_MAP.put("424d8240090000000000", FileTypeEnum.IMAGE); //24位位图(bmp)
        FILE_TYPE_MAP.put("424d8e1b030000000000", FileTypeEnum.IMAGE); //256色位图(bmp)
        FILE_TYPE_MAP.put("41433130313500000000", FileTypeEnum.IMAGE); //CAD (dwg)
        FILE_TYPE_MAP.put("3c21444f435459504520", FileTypeEnum.TXT); //HTML (html)
        FILE_TYPE_MAP.put("3c21646f637479706520", FileTypeEnum.TXT); //HTM (htm)
        FILE_TYPE_MAP.put("48544d4c207b0d0a0942", FileTypeEnum.TXT); //css
        FILE_TYPE_MAP.put("696b2e71623d696b2e71", FileTypeEnum.TXT); //js
        FILE_TYPE_MAP.put("7b5c727466315c616e73", FileTypeEnum.TXT); //Rich Text Format (rtf)
        FILE_TYPE_MAP.put("38425053000100000000", FileTypeEnum.OFFICE); //Photoshop (psd)
        FILE_TYPE_MAP.put("46726f6d3a203d3f6762", FileTypeEnum.OFFICE); //Email [Outlook Express 6] (eml)
        FILE_TYPE_MAP.put("d0cf11e0a1b11ae10000", FileTypeEnum.OFFICE); //MS Excel 注意：word、msi 和 excel的文件头一样
        FILE_TYPE_MAP.put("5374616E64617264204A", FileTypeEnum.OFFICE); //MS Access (mdb)
        FILE_TYPE_MAP.put("252150532D41646F6265", FileTypeEnum.OFFICE); // ps
        FILE_TYPE_MAP.put("255044462d312e350d0a", FileTypeEnum.OFFICE); //Adobe Acrobat (pdf)
        FILE_TYPE_MAP.put("2e524d46000000120001", FileTypeEnum.VIDEO); //rmvb/rm相同
        FILE_TYPE_MAP.put("464c5601050000000900", FileTypeEnum.VIDEO); //flv与f4v相同
        FILE_TYPE_MAP.put("00000020667479706d70", FileTypeEnum.VIDEO); // mp4
        FILE_TYPE_MAP.put("49443303000000002176", FileTypeEnum.AUDIO); // mp3
        FILE_TYPE_MAP.put("000001ba210001000180", FileTypeEnum.VIDEO); // mpg
        FILE_TYPE_MAP.put("3026b2758e66cf11a6d9", FileTypeEnum.VIDEO); //wmv与asf相同
        FILE_TYPE_MAP.put("52494646e27807005741", FileTypeEnum.AUDIO); //Wave (wav)
        FILE_TYPE_MAP.put("52494646d07d60074156", FileTypeEnum.VIDEO); // avi
        FILE_TYPE_MAP.put("4d546864000000060001", FileTypeEnum.AUDIO); //MIDI (mid)

        FILE_TYPE_MAP.put("504b0304140000000800", FileTypeEnum.OTHER); // zip
        FILE_TYPE_MAP.put("526172211a0700cf9073", FileTypeEnum.OTHER); //rar
        FILE_TYPE_MAP.put("235468697320636f6e66", FileTypeEnum.TXT); //ini
        FILE_TYPE_MAP.put("504b03040a0000000000", FileTypeEnum.OTHER); // jar
        FILE_TYPE_MAP.put("4d5a9000030000000400", FileTypeEnum.OTHER);//可执行文件
        FILE_TYPE_MAP.put("3c25402070616765206c", FileTypeEnum.TXT);//jsp文件
        FILE_TYPE_MAP.put("4d616e69666573742d56", FileTypeEnum.TXT);//MF文件
        FILE_TYPE_MAP.put("3c3f786d6c2076657273", FileTypeEnum.TXT);//xml文件
        FILE_TYPE_MAP.put("494e5345525420494e54", FileTypeEnum.TXT);//xml文件
        FILE_TYPE_MAP.put("7061636b616765207765", FileTypeEnum.TXT);//java文件
        FILE_TYPE_MAP.put("406563686f206f66660d", FileTypeEnum.OTHER);//bat文件
        FILE_TYPE_MAP.put("1f8b0800000000000000", FileTypeEnum.OTHER);//gz文件
        FILE_TYPE_MAP.put("6c6f67346a2e726f6f74", FileTypeEnum.TXT);//properties
        FILE_TYPE_MAP.put("cafebabe0000002e0041", FileTypeEnum.OTHER);//class
        FILE_TYPE_MAP.put("49545346030000006000", FileTypeEnum.OTHER);//chm
        FILE_TYPE_MAP.put("04000000010000001300", FileTypeEnum.OTHER);//mxp

        FILE_TYPE_MAP.put("504b0304140006000800", FileTypeEnum.OFFICE);//docx文件
        FILE_TYPE_MAP.put("6431303a637265617465", FileTypeEnum.OTHER);

        FILE_TYPE_MAP.put("6D6F6F76", FileTypeEnum.VIDEO); //Quicktime (mov)
        FILE_TYPE_MAP.put("FF575043", FileTypeEnum.OFFICE); //WordPerfect (wpd)
        FILE_TYPE_MAP.put("CFAD12FEC5FD746F", FileTypeEnum.OFFICE); //Outlook Express (dbx)
        FILE_TYPE_MAP.put("2142444E", FileTypeEnum.OFFICE); //Outlook (pst)
        FILE_TYPE_MAP.put("AC9EBD8F", FileTypeEnum.VIDEO); //Quicken (qdf)
        FILE_TYPE_MAP.put("E3828596", FileTypeEnum.TXT); //Windows Password (pwl)
        FILE_TYPE_MAP.put("2E7261FD", FileTypeEnum.AUDIO); //Real Audio (ram)
    }

    /**
     * 得到上传文件的文件头
     *
     * @param src
     * @return
     */
    private static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    /**
     * 安全的文件类型直接通过后缀检查
     *
     * @param ext
     * @param is
     * @return
     */
    public static FileTypeEnum getFileType(String ext, InputStream is) {
        FileTypeEnum fileType;
        ext = ext.toLowerCase();
        if (StringUtils.equals("txt", ext)) {
            fileType = FileTypeEnum.TXT;
        } else if (StringUtils.contains("jpg|jpeg|gif|png|bmp", ext)) {
            fileType = FileTypeEnum.IMAGE;
        } else if (StringUtils.contains("docx|pptx|ppsx|xlsx", ext)) {
            fileType = FileTypeEnum.OFFICE;
        } else {
            fileType = getFileType(is);
        }
        return fileType;
    }

    /**
     * 根据制定文件的文件头判断其文件类型
     * note:this method will close inputStream after it reads the leading 10 bytes
     *
     * @param is
     * @return
     */
    public static FileTypeEnum getFileType(InputStream is) {
        FileTypeEnum fileType = FileTypeEnum.OTHER;
        try {
            byte[] b = new byte[10];
            is.read(b, 0, b.length);
            String fileCode = bytesToHexString(b);
            LOGGER.info("fileCode:{}.", fileCode);

            //这种方法在字典的头代码不够位数的时候可以用但是速度相对慢一点
            Iterator<String> keyIter = FILE_TYPE_MAP.keySet().iterator();
            while (keyIter.hasNext()) {
                String key = keyIter.next();
                if (key.toLowerCase().startsWith(fileCode.toLowerCase()) || fileCode.toLowerCase().startsWith(key.toLowerCase())) {
                    fileType = FILE_TYPE_MAP.get(key);
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return fileType;
    }

}
