/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.handler.impl;

import com.yeepay.boot.components.utils.io.FileUtil;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.FileUploadHandler;
import com.yeepay.g3.yop.center.handler.entity.FileUploadResult;
import com.yeepay.g3.yop.center.utils.KeyGenerator;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.storage.CephUploadRequest;
import com.yeepay.g3.yop.frame.storage.Storage;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/12/13 下午7:10
 */
@Component
public class CephFileUploadHandler implements FileUploadHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CephFileUploadHandler.class);

    @Autowired
    private Storage storage;

    @Override
    public FileUploadResult upload(MultipartFile file, Context context) {
        String customerNo = context.getAppContext().getApp().getCustomerNo();
        // 现在的调用都是弱授权，2019-01-01后的商户不允许调用，所以仍然使用商户传递的appKey
        // 避免别名数据的迁移对商户端造成影响
        String appKey = context.getAppContext().getAppKey();
        String fileExt = StringUtils.substringAfterLast(file.getOriginalFilename(), ".");
        String fileName = generateUploadFileName(customerNo, appKey, fileExt);
        String apiGroup = context.getApiContext().getApiHandler().getApiDefinition().getGroup();
        String fileDir = generateRemoteFileDir(StringUtils.defaultIfBlank(apiGroup, "default"));
        Map<String, String> config = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_CEPH_CONFIG, Map.class);
        FileUploadResult result = FileUploadResult.newBuilder()
                .fileDir(fileDir)
                .fileName(fileName)
                .fileExt(fileExt)
                .fileLocation(ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_CEPH_RESOURCE_PATH, String.class)
                        + "/" + config.get("bucket")
                        + "/" + fileDir
                        + "/" + fileName)
                .build();

        try {
            CephUploadRequest cephUploadRequest = CephUploadRequest.newBuilder()
                    .address(config.get("ycsUrl"))
                    .secretKey(config.get("token"))
                    .bucket(config.get("bucket"))
                    .fileName(fileDir + "/" + fileName)
                    .inputStream(file.getInputStream())
                    .build();
            storage.upload(cephUploadRequest);
        } catch (Throwable t) {
            result.setThrowable(t);
            LOGGER.warn("Upload file fail. file:" + file.getOriginalFilename(), t);
        }
        return result;
    }

    private String generateRemoteFileDir(String apiGroup) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        StringBuilder builder = new StringBuilder();
        builder.append(FileUtil.normalize(apiGroup));
        builder.append("/").append(calendar.get(Calendar.YEAR));
        builder.append("/").append(calendar.get(Calendar.MONTH) + 1);
        builder.append("/").append(calendar.get(Calendar.DAY_OF_MONTH));
        return builder.toString();
    }

    private String generateUploadFileName(String customerNo, String appKey, String ext) {
        StringBuilder builder = new StringBuilder();
        builder.append(customerNo).append("-")
                .append(appKey).append("-")
                .append(KeyGenerator.generateKey())
                .append(".")
                .append(ext);

        return FileUtil.normalize(builder.toString());
    }
}
