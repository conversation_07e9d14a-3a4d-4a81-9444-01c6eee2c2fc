package com.yeepay.g3.yop.gateway.annotations;

import com.yeepay.g3.yop.ext.gateway.context.SDKVersion;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * title: sdk版本适配的<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/11/27 下午6:16
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface SDKVersionAdaptive {

    /**
     * 最小适配版本（>=min）
     *
     * @return
     */
    SDKVersion min() default SDKVersion.PAST;

    /**
     * 最大适配版本（<=max）
     */
    SDKVersion max() default SDKVersion.FUTURE;

}
