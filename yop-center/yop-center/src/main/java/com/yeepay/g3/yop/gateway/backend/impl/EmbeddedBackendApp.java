package com.yeepay.g3.yop.gateway.backend.impl;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.CharacterConstants;
import com.yeepay.g3.yop.frame.YopClassLoader;
import com.yeepay.g3.yop.frame.definition.ApiDefinition;
import com.yeepay.g3.yop.frame.exception.service.backend.BackendInitializationException;
import com.yeepay.g3.yop.frame.exception.service.backend.UnSupportedEndProtocolException;
import com.yeepay.g3.yop.frame.utils.ApplicationContextHolder;
import com.yeepay.g3.yop.ext.gateway.backend.ApiBackend;
import com.yeepay.g3.yop.gateway.backend.BackendApp;
import com.yeepay.g3.yop.gateway.backend.SpringBackend;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * title: 内嵌后端应用<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/8/2 11:47
 */
@Component
public class EmbeddedBackendApp implements BackendApp {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddedBackendApp.class);

    @Override
    public String getName() {
        return "yop-center-embedded";
    }

    @Override
    public ClassLoader getClassLoader() {
        return Thread.currentThread().getContextClassLoader();
    }

    @Override
    public ApiBackend createApiBackend(ApiDefinition apiDefinition) throws Exception {
        ApiBackend apiBackend = null;
        switch (apiDefinition.getEndProtocol()) {
            case SPRING:
                apiBackend = createSpringBackend(apiDefinition);
                break;
            default:
                throw new UnSupportedEndProtocolException();
        }
        return apiBackend;
    }

    private ApiBackend createSpringBackend(ApiDefinition apiDef) throws Exception {
        String endClass = apiDef.getEndClass();
        String endMethod = apiDef.getEndMethod();

        SpringBackend backend = new SpringBackend(apiDef.getBackendUri());
        String beanName = backend.getBeanName();
        Object proxy = ApplicationContextHolder.getBean(beanName);
        Class<?> localClass = null;
        if (proxy != null) {
            localClass = ApplicationContextHolder.getTargetClass(proxy);
        } else if (StringUtils.contains(endClass, CharacterConstants.DOT)) {
            localClass = Thread.currentThread().getContextClassLoader().loadClass(endClass);
            proxy = ApplicationContextHolder.getBean(localClass);
            if (proxy == null) {
                proxy = localClass.newInstance();
            }
        }
        if (proxy == null) {
            LOGGER.error("bean does not exist when createSpringBackend. beanName:{}, endClass:{}.", beanName, endClass);
            throw new BackendInitializationException();
        }
        backend.setProxy(proxy);
        backend.setEndMethod(YopClassLoader.getMethod(proxy.getClass(), endMethod));
        backend.setTargetMethod(YopClassLoader.getMethod(localClass, endMethod));
        return backend;
    }

    @Override
    public void close() {
        //do nothing
    }

}
