/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/16 下午3:59
 */
public class Part extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private Integer partNumber;

    private String eTag;

    public Integer getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(Integer partNumber) {
        this.partNumber = partNumber;
    }

    @JsonGetter("etag")
    public String getETag() {
        return eTag;
    }

    @JsonSetter("eTag")
    public void setETag(String eTag) {
        this.eTag = eTag;
    }
}
