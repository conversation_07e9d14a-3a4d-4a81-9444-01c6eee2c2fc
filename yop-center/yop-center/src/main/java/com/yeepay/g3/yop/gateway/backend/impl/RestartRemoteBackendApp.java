package com.yeepay.g3.yop.gateway.backend.impl;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.classloader.ClassProvider;
import com.yeepay.g3.yop.frame.classloader.RestartClassLoader;
import com.yeepay.g3.yop.frame.classloader.SmartClassLoader;
import com.yeepay.g3.yop.frame.classloader.provider.RedisClassProvider;
import com.yeepay.g3.yop.frame.classloader.provider.RemoteClassProvider;

/**
 * title: 可重启的后端应用 实现类<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/9/13 上午10:21
 */
public class RestartRemoteBackendApp extends RemoteBackendApp<SmartClassLoader> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RestartRemoteBackendApp.class);

    private final ClassProvider classProvider;

    public RestartRemoteBackendApp(String backendName, String endClass) {
        super(backendName, endClass);
        ClassProvider delegateClassProvider = new RemoteClassProvider(backendName, endClass);
        this.classProvider = new RedisClassProvider(backendName, endClass, delegateClassProvider);
    }

    @Override
    protected SmartClassLoader doGenerateRemoteClassLoader(String backendApp, String version, ClassLoader parentClassLoader) {
        BackendResource resource = getResource();
        ClassLoader oldClassLoader = null == resource ? null : resource.getClassLoader();

        LOGGER.info("create classLoader, type:RestartClassLoader, backendApp:{}, endClass:{}, version:{}", backendApp, endClasses, version);
        long oldVersion = classProvider.getVersion();
        classProvider.updateVersion(version);
        long newVersion = classProvider.getVersion();
        if (oldVersion == newVersion) {
            LOGGER.info("rebuild version not change, version:{}", newVersion);
            return null;
        }

        if (oldClassLoader instanceof SmartClassLoader) {
            return new RestartClassLoader(backendName, (SmartClassLoader) oldClassLoader, classProvider, parentClassLoader);
        } else {
            // TODO 冷启动
            return new RestartClassLoader(backendName, classProvider, parentClassLoader);
        }
    }

}
