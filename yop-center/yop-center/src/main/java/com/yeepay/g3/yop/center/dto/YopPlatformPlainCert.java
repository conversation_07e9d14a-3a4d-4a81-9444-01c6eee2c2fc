/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto;

import com.yeepay.g3.yop.frame.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * title: YopPlatformPlainCert<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/25 7:04 下午
 */
@Getter
@Setter
public class YopPlatformPlainCert extends BaseDTO {

    private static final long serialVersionUID = -1L;

    /**
     * 证书序列号
     */
    private String serialNo;

    /**
     * 证书生效时间
     */
    private Date effectiveDate;

    /**
     * 证书过期时间
     */
    private Date expireDate;

    /**
     * cer格式的证书
     */
    private String cert;
}
