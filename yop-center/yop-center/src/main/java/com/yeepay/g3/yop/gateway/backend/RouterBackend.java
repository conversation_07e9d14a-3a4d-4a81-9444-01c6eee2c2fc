/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend;

import com.yeepay.g3.yop.ext.gateway.backend.ApiBackend;
import com.yeepay.g3.yop.gateway.router.PredicateInputGenerator;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import com.yeepay.g3.yop.router.predicate.Predicate;
import com.yeepay.g3.yop.router.predicate.PredicateInput;
import com.yeepay.g3.yop.router.route.Router;
import com.yeepay.g3.yop.router.route.RouterWithPredicate;
import com.yeepay.g3.yop.router.utils.RouteConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: 新版API路由的后端配置<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/13 3:03 PM
 */
@AllArgsConstructor
@Slf4j
public class RouterBackend implements ApiBackend {

    private List<RouterWithPredicate> routerWithPredicates;

    private PredicateInputGenerator predicateInputGenerator;

    @Override
    public Object invoke(ServerWebExchange exchange) throws Throwable {
        PredicateInput predicateInput = predicateInputGenerator.generateInput(exchange);
        int size = routerWithPredicates.size();
        for (int i = 0; i < size; i++) {
            RouterWithPredicate routerWithPredicate = routerWithPredicates.get(i);
            Predicate predicate = routerWithPredicate.getPredicate();
            // 没有谓词直接通过。最后一个直接通过。
            if (null == predicate || i == size - 1 || predicate.apply(predicateInput)) {
                Router router = routerWithPredicate.getRouter();
                Object response = router.route(ServerWebExchangeUtils.getRouteContext(exchange));
                try {
                    Map<String, Object> responseMap = router.responseMapping(buildResponse(response));
                    return responseMap.get(RouteConstants.BODY);
                } catch (Exception exception) {
                    // 理论上不应该走到这里，为了响应结果映射的逻辑不产生坏的影响在这里加个catch
                    log.error("error happened when response mapping", exception);
                    return response;
                }
            }
        }
        return null;
    }

    private Map<String, Object> buildResponse(Object rawResponse) {
        Map<String, Object> result = new HashMap<>();
        result.put(RouteConstants.BODY, rawResponse);
        return result;
    }
}
