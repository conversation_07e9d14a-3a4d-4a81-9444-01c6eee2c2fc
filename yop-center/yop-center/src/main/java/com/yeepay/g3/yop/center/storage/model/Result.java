/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.storage.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/21 上午11:13
 */
public class Result implements Serializable {

    private Map<String, Object> data = new HashMap<String, Object>();

    private ObjectMetaData objectMetaData;

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public ObjectMetaData getObjectMetaData() {
        return objectMetaData;
    }

    public void setObjectMetaData(ObjectMetaData objectMetaData) {
        this.objectMetaData = objectMetaData;
    }
}
