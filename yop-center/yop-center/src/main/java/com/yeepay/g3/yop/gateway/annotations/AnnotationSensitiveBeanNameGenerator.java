package com.yeepay.g3.yop.gateway.annotations;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.core.annotation.AnnotationUtils;

import static com.yeepay.g3.yop.frame.CharacterConstants.*;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/26 下午7:11
 */
public class AnnotationSensitiveBeanNameGenerator extends AnnotationBeanNameGenerator {

    @Override
    public String generateBeanName(BeanDefinition definition, BeanDefinitionRegistry registry) {
        String beanName = super.generateBeanName(definition, registry);

        Class beanClass = null;
        try {
            beanClass = Thread.currentThread().getContextClassLoader().loadClass(definition.getBeanClassName());
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }

        SDKVersionAdaptive sdkVersionAdaptive = AnnotationUtils.findAnnotation(beanClass, SDKVersionAdaptive.class);
        if (sdkVersionAdaptive != null) {
            beanName += (DASH_LINE + LEFT_PARENTHESES + sdkVersionAdaptive.min() + DASH_LINE + sdkVersionAdaptive.max() + RIGHT_PARENTHESES);
        }
        beanName = "filter" + DASH_LINE + beanName;
        return beanName;
    }

}
