/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi.facade;

import com.yeepay.g3.yop.center.dto.YopPlatformCertQueryParam;
import com.yeepay.g3.yop.center.dto.YopPlatformCerts;
import com.yeepay.g3.yop.center.dto.YopPlatformPlainCerts;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/2/8 5:44 下午
 */
public interface YopPlatformCertQueryFacade {

    YopPlatformCerts find(YopPlatformCertQueryParam param);

    YopPlatformPlainCerts findPlainCert(YopPlatformCertQueryParam param);

}
