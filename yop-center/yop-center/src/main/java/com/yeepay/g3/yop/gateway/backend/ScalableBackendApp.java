package com.yeepay.g3.yop.gateway.backend;

/**
 * title: 可扩展的后端应用<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/8/2 11:33
 */
public interface ScalableBackendApp extends BackendApp {

    /**
     * version 版本号
     */
    void init(String version);

    /**
     * 记忆后端类
     *
     * @param endClass 后端类
     */
    boolean rememberEndClass(String endClass);

    /**
     * 后台处理，用于定时释放资源
     */
    void backGroundProgress();

    /**
     * 销毁上一个版本
     */
    void destroyPrevVersion();

}
