/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.yop.gateway.backend;

import com.alibaba.dubbo.rpc.RpcException;
import com.yeepay.boot.components.utils.Exceptions;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;

import java.io.Serializable;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/24 5:36 PM
 */
public class YopRemoteProxyHandler implements InvocationHandler, Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(YopRemoteProxyHandler.class);

    private static final long serialVersionUID = -1L;

    private Object target;

    private Object backup;

    public YopRemoteProxyHandler(Object target, Object backup) {
        this.target = target;
        this.backup = backup;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        Object result = null;
        try {
            // TODO 如果SOA不好使可以熔断20s
            LOGGER.debug("invoke rmi, method:{}, args:{}", method.getName(), args);
            result = method.invoke(target, args);
        } catch (Throwable e) {
            Throwable cause = Exceptions.getRootCause(Exceptions.unwrap(e));
            if (cause instanceof RpcException
                    && backup != null
                    && StringUtils.contains(cause.getMessage(),
                    "No provider available for the service")) {
                LOGGER.warn("change to backup due to:{}", cause.getMessage());
                result = method.invoke(backup, args);
            } else {
                throw e;
            }
        }
        return result;
    }

}
