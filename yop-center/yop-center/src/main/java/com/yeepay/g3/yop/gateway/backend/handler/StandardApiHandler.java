package com.yeepay.g3.yop.gateway.backend.handler;

import com.yeepay.g3.yop.ext.gateway.backend.ApiBackend;
import com.yeepay.g3.yop.ext.gateway.backend.handler.ApiHandler;
import com.yeepay.g3.yop.frame.definition.ApiDefinition;
import com.yeepay.g3.yop.frame.definition.ApiNewDTO;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.gateway.backend.ComposedRouterBackend;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;

/**
 * @author：wang.bao
 * @since：2015年3月5日 上午11:35:52
 * @version:
 */
public class StandardApiHandler implements ApiHandler {

    protected ApiDefinition apiDefinition;

    protected ApiNewDTO apiDTO;

    protected ApiBackend backend;

    protected boolean apiV2;

    protected boolean generic;

    public StandardApiHandler(ApiDefinition apiDefinition, ApiBackend backend) {
        this.apiDefinition = apiDefinition;
        this.backend = backend;
        this.apiV2 = false;
        this.generic = false;
    }

    public StandardApiHandler(ApiDefinition apiDefinition, ComposedRouterBackend backend) {
        this.apiDefinition = apiDefinition;
        this.backend = backend;
        this.apiV2 = false;
        this.generic = true;
    }

    public StandardApiHandler(ApiNewDTO apiDTO, ApiBackend backend) {
        this.apiDTO = apiDTO;
        this.backend = backend;
        this.apiV2 = true;
        this.generic = false;
    }

    @Override
    public Response handle(ServerWebExchange exchange) throws Throwable {
        Object invokeResult = backend.invoke(exchange);
        return new Response(invokeResult);
    }

    @Override
    public ApiDefinition getApiDefinition() {
        return apiDefinition;
    }

    @Override
    public ApiNewDTO getApiDTO() {
        return apiDTO;
    }

    @Override
    public ApiBackend getApiBackend() {
        return backend;
    }

    @Override
    public boolean isApiV2() {
        return apiV2;
    }

    @Override
    public boolean isGeneric() {
        return generic;
    }
}
