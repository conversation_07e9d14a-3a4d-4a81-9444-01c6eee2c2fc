/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.utils;

import com.yeepay.boot.components.utils.RandomUtil;
import com.yeepay.g3.yop.frame.CharacterConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * title: 调用链开关工具类<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/21 3:30 下午
 */
@Slf4j
public class InvokeSwitchUtils {

    public static boolean enableFilterChain(String customerNo, String apiUri, Map<String, Long> config) {
        if (MapUtils.isEmpty(config)) {
            log.info("empty invoke chain threshold config,invoke old chain");
            return false;
        }

        Long configThreshold = config.get(ConfigType.API_URI.name() + CharacterConstants.HASH + apiUri);
        if (Objects.isNull(configThreshold)) {
            configThreshold = config.get(ConfigType.API_GROUP.name() + CharacterConstants.HASH + getGroup(apiUri));
        }
        if (Objects.isNull(configThreshold)) {
            configThreshold = config.get(ConfigType.CUSTOMER_NO + CharacterConstants.HASH + customerNo);
        }

        if (Objects.isNull(configThreshold)) {
            configThreshold = config.get("*");
        }

        configThreshold = Objects.isNull(configThreshold) ? 0L : configThreshold;

        if (configThreshold <= 0) {
            return false;
        }
        if (configThreshold >= 10000) {
            return true;
        }
        return RandomUtil.nextInt(0, 10000) <= configThreshold;
    }

    public static String getGroup(String apiUri) {
        String[] strs = StringUtils.split(apiUri, CharacterConstants.SLASH);
        if (strs.length < 3) {
            return "";
        }
        return strs[2];
    }

    //开关的配置类型
    enum ConfigType {
        /**
         * 商编：根据商编切换
         */
        CUSTOMER_NO,
        /**
         * API分组
         */
        API_GROUP,
        /**
         * API Uri
         */
        API_URI
    }
}
