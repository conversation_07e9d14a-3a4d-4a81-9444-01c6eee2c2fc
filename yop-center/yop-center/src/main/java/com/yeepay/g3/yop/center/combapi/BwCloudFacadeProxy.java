package com.yeepay.g3.yop.center.combapi;

import com.baiwang.bop.request.impl.bizinfo.CompanySearchRequest;
import com.baiwang.bop.request.impl.invoice.common.InvoiceDetails;
import com.baiwang.bop.request.impl.invoice.impl.FormatfileBuildRequest;
import com.baiwang.bop.request.impl.invoice.impl.InvoiceInvalidRequest;
import com.baiwang.bop.request.impl.invoice.impl.InvoiceOpenRequest;
import com.baiwang.bop.respose.entity.FormatfileBuildResponse;
import com.baiwang.bop.respose.entity.InvoiceInvalidResponse;
import com.baiwang.bop.respose.entity.InvoiceOpenResponse;
import com.baiwang.bop.respose.entity.bizinfo.CompanySearchResponse;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.google.common.collect.Maps;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.yop.center.handler.BwCloudAPIHandler;
import com.yeepay.g3.yop.center.handler.entity.BWCloudResult;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.ContextUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;

/**
 * title: BwCloudFacadeProxy<br/>
 * description: 百望云开发票代理<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年9月21日 上午9:57:49
 */
public class BwCloudFacadeProxy {

    private final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    private final JavaType javaType = JSON_MAPPER.contructCollectionType(ArrayList.class, InvoiceDetails.class);

    private Map<String, BwCloudAPIHandler> apiHandlerMap;

    /**
     * 发票开具
     *
     * @param context
     * @param request
     * @param detailsListJsonStr 样例：[{"priceTaxMark":"0","goodsTaxItem":"","goodsTaxRate":"0.17","goodsDiscountLineNo":"","goodsExtendCode":"","goodsTotalPrice":"200.00","goodsLineNature":"0","goodsSpecification":"","goodsPrice":"","freeTaxMark":"","goodsQuantity":"","goodsUnit":"","goodsTotalTax":"34.00","goodsCode":"1020101000000000000","preferentialMark":"0","goodsName":"原煤","goodsLineNo":"1","vatSpecialManagement":""}]
     * @return
     * @throws JsonParseException
     * @throws JsonMappingException
     * @throws IOException
     */
    public BWCloudResult<InvoiceOpenResponse> issue(Context context, InvoiceOpenRequest request, String detailsListJsonStr) throws JsonParseException, JsonMappingException, IOException {
        String uri = context.getApiContext().getApiUri();
        String yopAppKey = ContextUtils.getAppId(context);
        request.setInvoiceDetailsList(JSON_MAPPER.fromJson(detailsListJsonStr, javaType));
        Map<String, Object> paramsmap = Maps.newHashMap();
        paramsmap.put("yopAppKey", yopAppKey);
        paramsmap.put("invoiceOpenRequest", request);
        return apiHandlerMap.get(uri).handle(paramsmap);
    }

    /**
     * 根据企业名称查税号
     *
     * @param context
     * @param request
     * @return
     */
    public BWCloudResult<CompanySearchResponse> search(Context context, CompanySearchRequest request) {
        String uri = context.getApiContext().getApiUri();
        String yopAppKey = ContextUtils.getAppId(context);
        Map<String, Object> paramsmap = Maps.newHashMap();
        paramsmap.put("yopAppKey", yopAppKey);
        paramsmap.put("companySearchRequest", request);
        return apiHandlerMap.get(uri).handle(paramsmap);
    }

    /**
     * 版式文件生成
     *
     * @param context
     * @param request
     * @return
     */
    public BWCloudResult<FormatfileBuildResponse> build(Context context, FormatfileBuildRequest request) {
        String uri = context.getApiContext().getApiUri();
        String yopAppKey = ContextUtils.getAppId(context);
        Map<String, Object> paramsmap = Maps.newHashMap();
        paramsmap.put("yopAppKey", yopAppKey);
        paramsmap.put("formatfileBuildRequest", request);
        return apiHandlerMap.get(uri).handle(paramsmap);
    }

    /**
     * 发票作废
     *
     * @param context
     * @param request
     * @return
     */
    public BWCloudResult<InvoiceInvalidResponse> invalid(Context context, InvoiceInvalidRequest request) {
        String uri = context.getApiContext().getApiUri();
        String yopAppKey = ContextUtils.getAppId(context);
        Map<String, Object> paramsmap = Maps.newHashMap();
        paramsmap.put("yopAppKey", yopAppKey);
        paramsmap.put("invoiceInvalidRequest", request);
        return apiHandlerMap.get(uri).handle(paramsmap);
    }

    public void setApiHandlerMap(Map<String, BwCloudAPIHandler> apiHandlerMap) {
        this.apiHandlerMap = apiHandlerMap;
    }

}
