/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi.facade.impl;

import com.yeepay.boot.components.utils.Exceptions;
import com.yeepay.boot.components.utils.concurrent.threadpool.ContextAwareTask;
import com.yeepay.boot.components.utils.concurrent.threadpool.ThreadPoolBuilder;
import com.yeepay.g3.yop.center.combapi.facade.YopReportFacade;
import com.yeepay.g3.yop.center.dto.report.YopReport;
import com.yeepay.g3.yop.center.dto.report.YopReportRequest;
import com.yeepay.g3.yop.center.dto.report.YopReportResponse;
import com.yeepay.g3.yop.frame.definition.YopReportProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * title: 客户端上报<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/7
 */
@Slf4j
@Component
public class YopReportFacadeImpl implements YopReportFacade {

    private RabbitTemplate rabbitTemplate;
    private ThreadPoolExecutor reportExecutor;

    @Override
    public YopReportResponse report(YopReportRequest request) {
        final List<YopReport> reports = request.getReports();
        if (CollectionUtils.isNotEmpty(reports)) {
            reportExecutor.submit(new ContextAwareTask(new ReportToMonitorTask(request)));
        }
        return YopReportResponse.DEFAULT_RESPONSE;
    }

    @Autowired
    public void setRabbitTemplate(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    @PostConstruct
    private void init() {
        reportExecutor = ThreadPoolBuilder.cachedPool()
                .threadNamePrefix("report-to-monitor")
                .min(5).max(50)
                .queueSize(3000)
                .keepAliveSecs(60)
                .shutdownTimeoutSecs(60)
                .allowCoreThreadTimeout(true)
                .rejectHanlder((r, executor) -> log.warn("report to monitor task is rejected."))
                .build();
    }

    class ReportToMonitorTask implements Runnable {

        private final YopReportRequest request;

        public ReportToMonitorTask(YopReportRequest request) {
            this.request = request;
        }

        @Override
        public void run() {
            final YopReportProto.YopReportList.Builder reportsBuilder = YopReportProto.YopReportList.newBuilder();
            reportsBuilder.setSessionId(request.getSessionId());
            reportsBuilder.setRequestId(request.getRequestId());
            reportsBuilder.setCustomerNo(request.getCustomerNo());
            reportsBuilder.setAppKey(request.getAppKey());
            reportsBuilder.setSdkLang(request.getSdkLang());
            reportsBuilder.setSdkVersion(request.getSdkVersion());
            List<YopReport> reports = request.getReports();
            for (YopReport report : reports) {
                try {
                    final YopReportProto.YopReport.Builder reportBuilder = YopReportProto.YopReport.newBuilder();
                    reportBuilder.setType(report.getType());
                    reportBuilder.setVersion(report.getVersion());
                    reportBuilder.setBeginDate(report.getBeginDate());
                    reportBuilder.setEndDate(report.getEndDate());
                    Map<String, Object> payload = (Map<String, Object>) report.getPayload();
                    payload.forEach((k,v) -> reportBuilder.putPayload(k, v.toString()));
                    reportsBuilder.addReports(reportBuilder.build());
                } catch (Exception e) {
                    log.warn("handle report error and ignore, value:" + report, e);
                }
            }
            try {
                rabbitTemplate.convertAndSend("exchange.direct.yop.center", "sdk.report", reportsBuilder.build());
            } catch (Throwable t) {
                log.error("error send report to mq, value:" + reports, Exceptions.optimizeStackTrace(t));
            }
        }
    }
}
