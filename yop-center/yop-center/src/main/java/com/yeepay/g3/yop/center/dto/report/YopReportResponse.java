/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto.report;

import lombok.Data;

import java.io.Serializable;

/**
 * title: 上报响应<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/6
 */
@Data
public class YopReportResponse implements Serializable {

    private static final long serialVersionUID = -1L;

    public static final String SUCCESS_STATUS = "success";
    public static final YopReportResponse DEFAULT_RESPONSE = new YopReportResponse();

    private String status = SUCCESS_STATUS;

    private String cmdType = "";

    private String cmd = "";
}
