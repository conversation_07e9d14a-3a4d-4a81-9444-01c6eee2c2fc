/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.utils.concurrent.threadpool.ThreadPoolBuilder;
import com.yeepay.g3.facade.yop.monitor.dto.FtpFileUploadRecordDTO;
import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.encrypt.Hex;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.FileUploadHandler;
import com.yeepay.g3.yop.center.handler.entity.FileUploadResult;
import com.yeepay.g3.yop.center.utils.file.FileTypeUtils;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.definition.FtpFileUploadLogProto;
import com.yeepay.g3.yop.frame.http.YopHttpServletRequestWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * title: 文件上传 服务<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR> wenkang.zhang
 * @version 1.0.0
 * @since 16/1/6 16:04
 */
@Deprecated
@Component
public class FileFacadeProxy {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileFacadeProxy.class);

    private final ThreadPoolExecutor FTP_FILE_UPLOAD_PERSISTENCE_EXECUTOR = ThreadPoolBuilder.cachedPool()
            .threadNamePrefix("ftpFileUpload-persistence")
            .min(2).max(10)
            .queueSize(1000)
            .keepAliveSecs(60)
            .shutdownTimeoutSecs(60)
            .rejectHanlder((r, executor) -> LOGGER.warn("ftpFileUpload persistence task is rejected."))
            .build();

    @Resource
    private FileUploadHandler cephFileUploadHandler;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 上传文件
     *
     * @param context
     * @return
     */
    public Map<String, Object> upload(Context context) {
        Map<String, Object> metaInfo = Maps.newHashMap();
        List<FtpFileUploadRecordDTO> files = new ArrayList<>();
        metaInfo.put("files", files);

        int totalCount = 0;
        int successCount = 0;

        String appKey = context.getAppContext().getAppKey();
        try {
            MultiValueMap<String, MultipartFile> multipartFiles = ((YopHttpServletRequestWrapper) context.getRawRequest())
                    .getMultipartFiles();
            if (null == multipartFiles) {
                metaInfo.put("total_count", totalCount);
                metaInfo.put("success_count", successCount);
                return metaInfo;
            }
            LOGGER.info("begin to upload file, appKey:{}, files:{}.", appKey, multipartFiles);
            for (MultipartFile item : multipartFiles.get("_file")) {
                ++totalCount;
                try {

                    FtpFileUploadRecordDTO record;
                    FileUploadResult result = cephFileUploadHandler.upload(item, context);
                    if (null != result.getThrowable()) {
                        continue;
                    }
                    record = generateFileRecord(appKey, item, result);
                    ++successCount;

                    files.add(record);
                } catch (Exception e) {
                    LOGGER.warn("Upload file fail. file:" + item.getOriginalFilename(), e);
                }
            }
            metaInfo.put("message", successCount > 0 ? "success" : "fail");
        } catch (Exception e) {
            LOGGER.warn("Upload file fail.", e);
            metaInfo.put("message", "fail");
        } finally {
            if (CollectionUtils.isNotEmpty(files)) {
                saveFtpFileUploadRecord(context, files);
            }
        }
        metaInfo.put("total_count", totalCount);
        metaInfo.put("success_count", successCount);
        return metaInfo;
    }

    private FtpFileUploadRecordDTO generateFileRecord(String appKey, MultipartFile item, FileUploadResult result)
            throws IOException {
        FtpFileUploadRecordDTO record = new FtpFileUploadRecordDTO();
        record.setFileId(StringUtils.substringBeforeLast(result.getFileName(), "."));
        record.setAppKey(appKey);
        // 利用文件头精确判断文件类型
        record.setFileType(FileTypeUtils.getFileType(result.getFileExt(), item.getInputStream()));
        record.setFileName(item.getOriginalFilename());
        record.setFileSize(item.getSize());
        record.setTempStorage(true);
        record.setCreatedDate(new Date());
        try {
            record.setMd5(Hex.toHex(DigestUtils.md5Digest(item.getBytes())));
        } catch (IOException e) {
            LOGGER.error("error when md5..", e);
        }
        record.setFileLocation(result.getFileLocation());
        return record;
    }

    private void saveFtpFileUploadRecord(Context context, List<FtpFileUploadRecordDTO> records) {
        try {
            FTP_FILE_UPLOAD_PERSISTENCE_EXECUTOR.submit(new FtpFileRecordPersistenceTask(context.getRequestId(),
                    ThreadContextUtils.getContext().getThreadUID(),
                    context.getAppContext().getAppKey(), records));
        } catch (Exception ex) {
            LOGGER.error("ftpFileUpload task submit failure, records:" + records + ".", ex);
        }
    }

    private class FtpFileRecordPersistenceTask implements Runnable {

        private final String requestId;

        private final String guid;

        private final String appKey;

        private final List<FtpFileUploadRecordDTO> records;

        private FtpFileRecordPersistenceTask(String requestId, String guid, String appKey, List<FtpFileUploadRecordDTO> records) {
            this.requestId = requestId;
            this.guid = guid;
            this.appKey = appKey;
            this.records = records;
        }

        @Override
        public void run() {
            FtpFileUploadLogProto.FtpFileUploadLog.Builder builder = FtpFileUploadLogProto.FtpFileUploadLog.newBuilder();
            builder.setRequestId(requestId).setGuid(guid).setAppKey(appKey);
            for (FtpFileUploadRecordDTO record : records) {
                builder.addFiles(FtpFileUploadLogProto.FtpFileUploadLog.FtpFileInfo.newBuilder()
                        .setFileId(record.getFileId())
                        .setFileType(record.getFileType().getValue())
                        .setFileName(record.getFileName())
                        .setFileLocation(record.getFileLocation())
                        .setFileSize(record.getFileSize())
                        .setMd5(record.getMd5())
                        .setTempStorage(record.getTempStorage())
                        .setUploadedTime(record.getCreatedDate().getTime()).build());
            }
            rabbitTemplate.convertAndSend("exchange.direct.yop.center", "ftpFileUpload.record", builder.build());
        }
    }

}
