package com.yeepay.g3.yop.center.handler;

import com.baiwang.bop.client.BopException;
import com.baiwang.bop.client.ILoginClient;
import com.baiwang.bop.client.impl.PostLogin;
import com.baiwang.bop.request.impl.LoginRequest;
import com.baiwang.bop.respose.entity.LoginResponse;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.center.handler.entity.BWCloudResult;
import com.yeepay.g3.yop.center.handler.entity.BwConfig;
import com.yeepay.g3.yop.center.handler.entity.BwToken;
import com.yeepay.g3.yop.frame.utils.ExceptionUtils;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * title: AbstractBwCloudAPIHandler<br/>
 * description: 商户登录授权<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年9月21日 上午10:06:53
 */
public abstract class AbstractBwCloudAPIHandler<T> implements BwCloudAPIHandler<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractBwCloudAPIHandler.class);

    protected static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    public static final int CACHE_MAX_SIZE = 10;
    public static final int CACHE_EXPIRE_AFTER_ACCESS = 86400;
    public static final int CACHE_REFRESH_AFTER_ACCESS = 300;

    public static final int TOKEN_EXPIRES_BIAS = 100;
    public static final String SERVICE_URL = "serviceUrl";
    public static final String RES_SUCCESS = "200";
    public static final String RES_FAILURE = "500";

    private final ConcurrentHashMap<String, Object> parallelLockMap = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<String, BwToken> bwcloudToken = new ConcurrentHashMap<>();

    private LoadingCache<String, BwConfig> bwConfigCahce = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterAccess(CACHE_EXPIRE_AFTER_ACCESS, TimeUnit.SECONDS)
            .refreshAfterWrite(CACHE_REFRESH_AFTER_ACCESS, TimeUnit.SECONDS)
            .recordStats()
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader() {
            @Override
            public Object load(Object key) throws Exception {
                Map<String, String> bwcloudConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_BWCLOUD_CONFIG);
                String configMapJsonStr = bwcloudConfig.get(key);
                if (StringUtils.isBlank(configMapJsonStr)) {
                    throw new RuntimeException("应用(" + key + ")未进行发票账户信息配置");
                }
                BwConfig bwConfig = JSON_MAPPER.fromJson(configMapJsonStr, BwConfig.class);
                bwConfig.setServerUrl(bwcloudConfig.get(SERVICE_URL));
                return bwConfig;
            }
        };
    }

    @Override
    public BWCloudResult<T> handle(Map<String, Object> params) {
        BWCloudResult<T> result = new BWCloudResult<T>();
        String appKey = (String) params.get("yopAppKey");

        BwConfig bwConfig = null;
        try {
            bwConfig = bwConfigCahce.get(appKey);
        } catch (Throwable e) {
            ExceptionUtils.optimizeStackTraceForUnknownException(e);
            LOGGER.info("error when get bwConfig, appKey:" + appKey, e);
        }

        BwToken currentToken = bwcloudToken.get(appKey);
        if (!verify(currentToken) || isExpire(currentToken)) {
            currentToken = generateToken(bwConfig, appKey);
        }

        try {
            return this.execute(bwConfig, currentToken.getAccessToken(), params);
        } catch (BopException e) {
            LOGGER.error("BWCloud invoke error", e);
            result.setReturnCode(e.getErrCode());
            result.setReturnMessages(e.getErrMsg());
            result.setReturnSubCode(e.getSubCode());
            result.setReturnSubMessage(e.getSubMessage());
        } catch (Exception e) {
            LOGGER.error("百望云接口调用出错", e);
            result.setReturnCode(RES_FAILURE);
            result.setReturnMessages(e.getMessage());
        }
        return result;
    }

    private BwToken generateToken(BwConfig bwConfig, String appKey) {
        // TODO
        // bwcloudToken.computeIfAbsent ()
        synchronized (getLock(appKey)) {
            BwToken bwToken = bwcloudToken.get(appKey);
            if (verify(bwToken) && !isExpire(bwToken)) {
                return bwToken;
            }
            bwToken = new BwToken();
            String serviceUrl = bwConfig.getServerUrl();
            ILoginClient loginClient = new PostLogin(serviceUrl);
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setAppkey(bwConfig.getAppKey());
            loginRequest.setAppSecret(bwConfig.getAppSecret());
            loginRequest.setUserName(bwConfig.getUsername());
            loginRequest.setPasswordMd5(bwConfig.getPassword());
            loginRequest.setUserSalt(bwConfig.getUserSalt());
            try {
                LoginResponse loginResponse = loginClient.login(loginRequest);
                LOGGER.info("BWCloud token response {}.", loginResponse);
                bwToken.setAccessToken(loginResponse.getAccess_token());
                long newTime = System.currentTimeMillis() + loginResponse.getExpires_in() - TOKEN_EXPIRES_BIAS;
                bwToken.setExpireDate(new Date(newTime));
                bwcloudToken.put(appKey, bwToken);
                return bwToken;
            } catch (Throwable e) {
                LOGGER.error("BWCloud invoke error", e);
            }
            return bwToken;
        }
    }

    protected Object getLock(String backendName) {
        Object lock = this;
        if (parallelLockMap != null || parallelLockMap.get(backendName) == null) {
            Object newLock = new Object();
            lock = parallelLockMap.putIfAbsent(backendName, newLock);
            if (lock == null) {
                lock = newLock;
            }
        }
        return lock;
    }

    private boolean verify(BwToken token) {
        if (token == null || StringUtils.isEmpty(token.getAccessToken())) {
            return false;
        }
        return true;
    }

    private boolean isExpire(BwToken token) {
        return token.getExpireDate().compareTo(new Date()) <= 0;
    }


    /**
     * 百望云接口调用
     *
     * @param token  登录token
     * @param params 调用参数
     * @return
     */
    protected abstract BWCloudResult<T> execute(BwConfig bwConfig, String token, Map<String, Object> params);

}
