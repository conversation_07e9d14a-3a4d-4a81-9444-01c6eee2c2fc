package com.yeepay.g3.yop.center.handler.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title: BWCloudResult<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017年9月22日 下午7:04:41
 */
public class BWCloudResult<T> implements Serializable {

    private static final long serialVersionUID = -4101478231660145613L;

    private String returnCode;

    private String returnSubCode;

    private String returnMessages;

    private String returnSubMessage;

    private T returnData;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessages() {
        return returnMessages;
    }

    public void setReturnMessages(String returnMessages) {
        this.returnMessages = returnMessages;
    }

    public T getReturnData() {
        return returnData;
    }

    public void setReturnData(T returnData) {
        this.returnData = returnData;
    }

    public String getReturnSubCode() {
        return returnSubCode;
    }

    public void setReturnSubCode(String returnSubCode) {
        this.returnSubCode = returnSubCode;
    }

    public String getReturnSubMessage() {
        return returnSubMessage;
    }

    public void setReturnSubMessage(String returnSubMessage) {
        this.returnSubMessage = returnSubMessage;
    }

    @Override
    public String toString() {
        try {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
        } catch (Exception e) {
            return super.toString();
        }
    }

}
