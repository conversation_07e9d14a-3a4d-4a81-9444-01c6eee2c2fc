package com.yeepay.g3.yop.gateway.backend.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.facade.yop.sys.dto.BackendAppDTO;
import com.yeepay.g3.facade.yop.sys.facade.BackendAppFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.frame.utils.EnvUtil;
import com.yeepay.g3.yop.gateway.backend.BackendApp;
import com.yeepay.g3.yop.gateway.backend.BackendAppFactory;
import com.yeepay.g3.yop.gateway.backend.ScalableBackendApp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * title: 后端应用工厂<br/>
 * description: 目前后端应用由于是全局存在（后期可以加缓存），所以后端应用的资源释放暂时<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 18/11/23 上午11:04
 */
@Component
public class DefaultBackendAppFactory implements BackendAppFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultBackendAppFactory.class);

    private final ConcurrentMap<String, ScalableBackendApp> backendAppRegistry = Maps.newConcurrentMap();

    private final ScheduledExecutorService backGroundExecutor = Executors.newSingleThreadScheduledExecutor();

    private final BackendAppFacade backendAppFacade = RemoteServiceFactory.getService(BackendAppFacade.class);

    @Autowired
    private BackendApp embeddedBackendApp;

    @Override
    public ScalableBackendApp getRemoteBackendApp(final String backendAppName, final String endClass) {
        if (StringUtils.isBlank(backendAppName)) {
            return null;
        }

        ScalableBackendApp backendApp = backendAppRegistry.get(backendAppName);
        // 更新事件不传递 endClass，避免先接到更新事件导致未初始化 endClass
        if (StringUtils.isNotBlank(endClass)) {
            if (null == backendApp) {
                backendApp = backendAppRegistry.computeIfAbsent(backendAppName, n -> createBackendApp(backendAppName, endClass));
            } else {
                backendApp.rememberEndClass(endClass);
            }
        }
        return backendApp;
    }

    private ScalableBackendApp createBackendApp(String backendName, String endClass) {
        ScalableBackendApp backendApp = new RestartRemoteBackendApp(backendName, endClass);
        String version = null;
        String env = EnvUtil.getCurrentEnv();
        try {
            BackendAppDTO backendAppDTO = backendAppFacade.findByBackendCode(backendName);
            if (null == backendAppDTO) {
                LOGGER.warn("backend app don't exists, backendCode:{}", backendName);
            } else {
                version = backendAppDTO.getBackendVersion().get(env);
                LOGGER.info("load backend version from yop-sys success, backendCode:{}, env:{}, version:{}", backendName, env, version);
            }
        } catch (Exception e) {
            LOGGER.warn("load backend version from yop-sys failed, backendCode:" + backendName + ", env:" + env, e);
        }
        // 降级处理
        if (StringUtils.isBlank(version)) {
            version = String.valueOf(System.currentTimeMillis());
            LOGGER.warn("load backend version from local, backendCode:{}, env:{}, version:{}", backendName, env, version);
        }
        // 加载最新的类文件
        // TODO 异步+预热
        backendApp.init(version);

        return backendApp;
    }

    @Override
    public BackendApp getEmbeddedApp() {
        return embeddedBackendApp;
    }


    @PostConstruct
    public void init() {
        backGroundExecutor.scheduleAtFixedRate(() -> {
            for (ScalableBackendApp backendApp : backendAppRegistry.values()) {
                try {
                    backendApp.backGroundProgress();
                } catch (Exception ex) {
                    LOGGER.error("backGroundProcess failure, backendApp:" + backendApp.getName(), ex);
                }
            }
        }, 60L, 60L, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        //立刻结束后台任务，尚未完成的清理工作由close完成
        backGroundExecutor.shutdownNow();
        if (backendAppRegistry.size() > 0) {
            backendAppRegistry.values().forEach(backendApp -> {
                try {
                    backendApp.close();
                } catch (Exception ex) {
                    LOGGER.error("unexpected error occurs when close backendApp.", ex);
                }
            });
        }
    }

}
