package com.yeepay.g3.yop.gateway.backend;

import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;

/**
 * <pre>
 * 功能说明：本地spring bean调用
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class SpringBackend extends MethodInvokeBackend {

    /**
     * 端点bean名字
     */
    private String beanName;

    /**
     * 目标方法，非代理类的方法，用于支持方法参数注解
     */
    private Method targetMethod;

    public SpringBackend(String uri) {
        super();
        if (StringUtils.isBlank(uri)) {
            throw new YeepayRuntimeException("BackendUri must be specified");
        }
        beanName = new BackendURI(uri).getDomain();
    }

    public String getBeanName() {
        return beanName;
    }

    public Method getTargetMethod() {
        return targetMethod;
    }

    public void setTargetMethod(Method targetMethod) {
        this.targetMethod = targetMethod;
    }
}
