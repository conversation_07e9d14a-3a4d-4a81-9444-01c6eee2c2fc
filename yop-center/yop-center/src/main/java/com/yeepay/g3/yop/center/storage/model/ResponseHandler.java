/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.storage.model;

import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.storage.sdk.exception.StorageException;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import java.io.Closeable;
import java.io.IOException;
import java.util.Map;


/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/21 上午11:10
 */
public class ResponseHandler {

    private static final JsonMapper jsonMapper = JsonMapper.nonEmptyMapper();

    private static final String API_HEADER_PREFIX = "x-yos";

    public static Result handle(HttpResponse response) throws IOException {
        try {
            Result result = new Result();
            Header[] headers = response.getAllHeaders();
            ObjectMetaData objectMetaData = new ObjectMetaData();
            result.setObjectMetaData(objectMetaData);
            for (Header header : headers) {
                if (header.getName().startsWith(API_HEADER_PREFIX)) {
                    objectMetaData.setHeader(header.getName(), header.getValue());
                }
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                Map data = jsonMapper.fromJson(EntityUtils.toString(entity, "UTF-8"), Map.class);
                result.setData(data);
            }
            return result;
        } catch (Exception e) {
            throw new StorageException("client exception", e);
        } finally {
            if (response != null && response instanceof Closeable) {
                try {
                    ((Closeable) response).close();
                } catch (IOException e) {
                    throw new IOException(e);
                }
            }
        }
    }

    public static Result handleError(HttpResponse response) throws IOException {
        StorageException exception = new StorageException(response.getStatusLine().getStatusCode(), response.getStatusLine().getReasonPhrase());
        try {
            HttpEntity entity = response.getEntity();
            if (entity != null && entity.isStreaming()) {
                Map data = jsonMapper.fromJson(EntityUtils.toString(entity, "UTF-8"), Map.class);
                if (data != null && !data.isEmpty()) {
                    exception = new StorageException((Integer) data.get("code"), (String) data.get("message"));
                }
            }
            throw exception;
        } catch (IOException e) {
            throw new StorageException("client exception", e);
        } finally {
            if (response instanceof Closeable) {
                ((Closeable) response).close();
            }
        }
    }
}
