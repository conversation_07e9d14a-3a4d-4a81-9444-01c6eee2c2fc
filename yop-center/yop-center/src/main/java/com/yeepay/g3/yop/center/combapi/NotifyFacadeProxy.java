/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi;

import com.fasterxml.jackson.databind.JavaType;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.notifier.NotifyFacade;
import com.yeepay.g3.facade.notifier.dto.*;
import com.yeepay.g3.utils.common.encrypt.Digest;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.ContextUtils;
import com.yeepay.g3.yop.frame.context.impl.HmacContext;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 下行短信服务本地API<br>
 * 因后端NotifyFacade还有数据防篡改验证，且签名算法不一致，单纯的API代理无法直接支持
 *
 * @author：wang.bao
 * @since：2015年5月4日 下午5:00:20
 * @version:
 */
@Component
public class NotifyFacadeProxy {

    private NotifyFacade notifyFacade = RemoteServiceFactory.getService(NotifyFacade.class);

    private final JsonMapper jsonMapper = JsonMapper.nonEmptyMapper();

    private final JavaType messageParamType = jsonMapper.contructMapType(HashMap.class, String.class, Object.class);

    @Deprecated
    @SuppressWarnings("unchecked")
    public NotifyResultDTO notify(
            Context context,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "secret", required = false) String secret,
            @RequestParam(value = "notifyRule") String notifyRuleName,
            @RequestParam(value = "recipients") List<String> recipients,
            @RequestParam(value = "content") String message,
            @RequestParam(value = "extNum", required = false) String extNum,
            @RequestParam(value = "feeSubject", required = false) String feeSubject) {
        HmacContext hmacContext = (HmacContext) context.getAuthenticateContext();
        if (StringUtils.isEmpty(userName) || StringUtils.isEmpty(secret)) {
            userName = ContextUtils.getAppId(context);
            secret = hmacContext.getAppSecret();
        }
        return doNotify(userName, secret, notifyRuleName, recipients, message, extNum, feeSubject);
    }

    @Deprecated
    public NotifyResultDTO notify(
            Context context,
            @RequestParam(value = "notifyRule") String notifyRuleName,
            @RequestParam(value = "recipients") List<String> recipients,
            @RequestParam(value = "content") String message,
            @RequestParam(value = "extNum", required = false) String extNum,
            @RequestParam(value = "feeSubject", required = false) String feeSubject) {
        HmacContext hmacContext = (HmacContext) context.getAuthenticateContext();
        String userName = ContextUtils.getAppId(context);
        String secret = hmacContext.getAppSecret();
        return doNotify(userName, secret, notifyRuleName, recipients, message, extNum, feeSubject);
    }


    @SuppressWarnings("unchecked")
    public NotifyResponse smsNotify(Context context,
                                    @RequestParam(value = "notifyRule") String notifyRuleName,
                                    @RequestParam(value = "recipients") List<String> recipients,
                                    @RequestParam(value = "content") String message) {
        Map<String, String> notifyConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_CENTER_NOTIFY_CONFIG);
        String userName = notifyConfig.get("userName");
        String secretKey = notifyConfig.get("secretKey");
        SmsNotifyRequest request = SmsNotifyRequest.Builder.aSmsNotifyRequest()
                .withUserName(userName)
                .withRequestCustomerId(ContextUtils.getAppId(context))
                .withFlowNo(context.getRequestId())
                .withNotifyRuleName(notifyRuleName)
                .withRecipients(recipients)
                .withMessageParams(generateMessageParam(message))
                .withSign(getSign(notifyRuleName, recipients, userName, secretKey))
                .build();
        return notifyFacade.smsNotify(request);
    }

    @SuppressWarnings("unchecked")
    public NotifyResponse mailNotify(Context context,
                                     @RequestParam(value = "notifyRule") String notifyRuleName,
                                     @RequestParam(value = "recipients") List<String> recipients,
                                     @RequestParam(value = "content") String message) {

        Map<String, String> notifyConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_CENTER_NOTIFY_CONFIG);
        String userName = notifyConfig.get("userName");
        String secretKey = notifyConfig.get("secretKey");
        MailNotifyRequest request = MailNotifyRequest.Builder.aMailNotifyRequest()
                .withUserName(userName)
                .withRequestCustomerId(ContextUtils.getAppId(context))
                .withFlowNo(context.getRequestId())
                .withNotifyRuleName(notifyRuleName)
                .withRecipients(recipients)
                .withMessageParams(generateMessageParam(message))
                .withSign(getSign(notifyRuleName, recipients, userName, secretKey))
                .build();
        return notifyFacade.mailNotify(request);
    }

    private String getSign(String notifyRuleName, List<String> recipients, String userName, String secretKey) {
        return Digest.md5Digest(userName + notifyRuleName
                + StringUtils.join(recipients, ",") + secretKey);
    }


    private Map<String, Object> generateMessageParam(String message) {
        try {
            if (StringUtils.startsWith(message, "{")) {
                return jsonMapper.fromJson(message, messageParamType);
            } else {
                Map<String, Object> paramMap = new HashMap<String, Object>();
                paramMap.put("message", message);
                return paramMap;
            }
        } catch (Exception ex) {
            throw new YeepayRuntimeException("illegal argument,message:{0}.", message);
        }
    }

    @SuppressWarnings("unchecked")
    private NotifyResultDTO doNotify(String userName,
                                     String secret,
                                     String notifyRuleName,
                                     List<String> recipients,
                                     String message,
                                     String extNum,
                                     String feeSubject) {
        NotifyFeature feature = new NotifyFeature();
        feature.setFeeSubject(feeSubject);
        feature.setExtNo(extNum);
        Map<String, Object> messages = new HashMap<String, Object>();
        if (message.startsWith("{")) {
            messages = JSONUtils.jsonToMap(message, String.class, null);
        } else {
            messages.put("message", message);
        }
        String sign = Digest.md5Digest(userName + notifyRuleName
                + StringUtils.join(recipients, ",") + secret + extNum);
        return notifyFacade.notify(userName, sign, notifyRuleName,
                recipients, messages, feature);
    }


}
