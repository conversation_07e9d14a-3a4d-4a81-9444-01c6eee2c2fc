/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.dto;

import com.yeepay.g3.yop.frame.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/2/19 2:45 下午
 */
@Getter
@Setter
public class YopPlatformCerts extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private List<YopPlatformCert> data;

}
