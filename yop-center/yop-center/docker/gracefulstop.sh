#!/bin/bash

if [ "X$ENABLE_SCRIPT_DEBUG" == "XY" -o "X$PIPELINESTYPE" == "XK8S" ]; then
    set -x
fi
LOCAL_IP=`ifconfig eth0 | grep "inet " | awk '{print $2}' | cut -d: -f2`
for i in {1..3}
do
code=`curl -I -X GET --connect-timeout 15  -m 10 -o /dev/null -s -w %{http_code} http://soa.core.3g:8080/soa-center-hessian/addresses/disable?address=${LOCAL_IP}\&operator=syspub `
if [ $code -eq 200 ];then
echo "$code"
echo "success";
break
else
echo "faild";
sleep 3
fi
done

sleep 90

# alpine、centos
#kill -s 15 `ps -ef | grep java | head -1 | awk '{print $2}'`