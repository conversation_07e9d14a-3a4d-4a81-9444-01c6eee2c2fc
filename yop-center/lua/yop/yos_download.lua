local jwt_decoder = require "yop.jwt_parser"
local http = require "resty.http"

local function retrieve_token()
    local args = ngx.req.get_uri_args();
    if not args.token
    then
        sendErrResponse(400, "miss token")
    end
    return args.token
end

local function forwardResponse(url)
    local httpc = http.new()
    local res = httpc:request_uri(url, {
        ssl_verify = false,
        method = "GET"
    })
    if not res then
        sendErrResponse(500, "error")
    end
    ngx.status = res.status
    for k, v in pairs(res.headers) do
        if k ~= "Transfer-Encoding" and k ~= "Connection" then
            ngx.header[k] = v;
        end
    end
    ngx.say(res.body)
    httpc:close()
end

local function sendErrResponse(code, content)
    ngx.status = code
    ngx.header['Content-Type'] = 'text/plain';
    ngx.say(content)
    ngx.exit(code)
end

local urlPrefixTable = {
    QA_CEPH = "https://qastaticres.yeepay.com",
    CEPH = "https://staticres.yeepay.com"
}

local function do_forward()
    local token = retrieve_token()
    if not token then
        sendErrResponse(401, "token miss");
    end
    local jwt = jwt_decoder:new(token)
    if not jwt then
        sendErrResponse(401, "token illegal");
    end

    -- Verify the JWT registered claims
    local ok_claims = jwt:verify_registered_claims({ "exp" })
    if not ok_claims then
        sendErrResponse(401, "token expired");
        return ;
    end

    -- Now verify the JWT signature
    local jwt_secret_value = "CR1o4wyKps8ZwGR9"
    jwt_secret_value = jwt:base64_decode(jwt_secret_value)
    jwt:verify_signature(jwt_secret_value)

    local url_type = jwt.claims.url_type;
    local prefix = urlPrefixTable[url_type];
    local url_path = jwt.claims.url_path;
    if not prefix or not url_path then
        ngx.status = 404
        return ngx.exit(ngx.HTTP_NOT_FOUND)
    end

    local url = prefix .. url_path;
    forwardResponse(url);
end

do_forward();