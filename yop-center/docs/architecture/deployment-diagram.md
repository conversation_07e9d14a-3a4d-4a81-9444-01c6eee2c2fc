# YOP Center 部署图

## 部署拓扑图

```mermaid
graph TB
    subgraph "外部网络 External Network"
        A[互联网用户<br/>Internet Users]
        B[合作伙伴<br/>Partners]
        C[第三方系统<br/>Third-party Systems]
    end
    
    subgraph "DMZ区域 DMZ Zone"
        D[防火墙<br/>Firewall]
        E[负载均衡器<br/>Load Balancer<br/>Nginx/HAProxy]
        F[Web应用防火墙<br/>WAF]
    end
    
    subgraph "应用服务区 Application Service Zone"
        G[Web服务器集群<br/>Web Server Cluster]
        H[应用服务器集群<br/>Application Server Cluster]
        I[API网关集群<br/>API Gateway Cluster]
    end
    
    subgraph "业务服务区 Business Service Zone"
        J[支付服务集群<br/>Payment Service Cluster]
        K[账户服务集群<br/>Account Service Cluster]
        L[风控服务集群<br/>Risk Control Service Cluster]
        M[通知服务集群<br/>Notification Service Cluster]
    end
    
    subgraph "数据服务区 Data Service Zone"
        N[数据库集群<br/>Database Cluster<br/>DB2 RAC]
        O[缓存集群<br/>Cache Cluster<br/>Redis Sentinel]
        P[配置中心集群<br/>Configuration Center<br/>ZooKeeper Cluster]
        Q[消息队列集群<br/>Message Queue Cluster]
    end
    
    subgraph "监控运维区 Monitoring & Operations Zone"
        R[监控系统<br/>Monitoring System]
        S[日志系统<br/>Logging System]
        T[运维管理平台<br/>Operations Management Platform]
        U[备份系统<br/>Backup System]
    end
    
    %% 网络连接
    A --> D
    B --> D
    C --> D
    
    D --> F
    F --> E
    E --> G
    
    G --> H
    H --> I
    
    I --> J
    I --> K
    I --> L
    I --> M
    
    J --> N
    K --> N
    L --> O
    M --> O
    
    I --> P
    J --> Q
    
    H --> R
    I --> S
    N --> T
    N --> U
    
    %% 样式定义
    classDef external fill:#ffebee
    classDef dmz fill:#e8f5e8
    classDef application fill:#e3f2fd
    classDef business fill:#f3e5f5
    classDef data fill:#fff8e1
    classDef monitoring fill:#f1f8e9
    
    class A,B,C external
    class D,E,F dmz
    class G,H,I application
    class J,K,L,M business
    class N,O,P,Q data
    class R,S,T,U monitoring
```

## 环境说明

### 开发环境 (Development)

```mermaid
graph TB
    subgraph "开发环境 Development Environment"
        A[开发者本地<br/>Developer Local]
        B[开发服务器<br/>Development Server]
        C[开发数据库<br/>Development Database]
        D[开发Redis<br/>Development Redis]
    end
    
    A --> B
    B --> C
    B --> D
    
    subgraph "配置特点 Configuration Features"
        E[单机部署<br/>Single Instance Deployment]
        F[内存数据库<br/>In-Memory Database]
        G[本地缓存<br/>Local Cache]
        H[调试模式<br/>Debug Mode]
    end
```

### 测试环境 (Testing)

```mermaid
graph TB
    subgraph "测试环境 Testing Environment"
        A[测试负载均衡<br/>Test Load Balancer]
        B[测试应用服务器<br/>Test Application Server]
        C[测试数据库<br/>Test Database]
        D[测试Redis<br/>Test Redis]
        E[测试ZooKeeper<br/>Test ZooKeeper]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    
    subgraph "测试特性 Testing Features"
        F[模拟生产环境<br/>Production-like Environment]
        G[自动化测试<br/>Automated Testing]
        H[性能测试<br/>Performance Testing]
        I[压力测试<br/>Stress Testing]
    end
```

### 生产环境 (Production)

```mermaid
graph TB
    subgraph "生产环境 Production Environment"
        A[生产负载均衡集群<br/>Production Load Balancer Cluster]
        B[生产应用服务器集群<br/>Production Application Server Cluster]
        C[生产数据库集群<br/>Production Database Cluster]
        D[生产Redis集群<br/>Production Redis Cluster]
        E[生产ZooKeeper集群<br/>Production ZooKeeper Cluster]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    
    subgraph "生产特性 Production Features"
        F[高可用部署<br/>High Availability Deployment]
        G[自动故障转移<br/>Automatic Failover]
        H[实时监控<br/>Real-time Monitoring]
        I[自动扩缩容<br/>Auto Scaling]
    end
```

## 容器化部署架构

```mermaid
graph TB
    subgraph "Kubernetes集群 Kubernetes Cluster"
        subgraph "命名空间: yop-system"
            A[Ingress Controller<br/>入口控制器]
            B[Service Mesh<br/>服务网格<br/>Istio]
        end
        
        subgraph "命名空间: yop-gateway"
            C[Gateway Deployment<br/>网关部署]
            D[Gateway Service<br/>网关服务]
            E[Gateway ConfigMap<br/>网关配置]
        end
        
        subgraph "命名空间: yop-business"
            F[Business Services<br/>业务服务]
            G[Business ConfigMaps<br/>业务配置]
            H[Business Secrets<br/>业务密钥]
        end
        
        subgraph "命名空间: yop-data"
            I[Database StatefulSet<br/>数据库有状态集]
            J[Redis Deployment<br/>Redis部署]
            K[ZooKeeper StatefulSet<br/>ZooKeeper有状态集]
        end
        
        subgraph "命名空间: yop-monitoring"
            L[Prometheus<br/>监控系统]
            M[Grafana<br/>可视化系统]
            N[ELK Stack<br/>日志系统]
        end
    end
    
    A --> B
    B --> C
    C --> D
    D --> F
    F --> I
    F --> J
    F --> K
    
    C --> L
    F --> M
    I --> N
```

## 网络架构图

```mermaid
graph TB
    subgraph "公网 Public Network"
        A[CDN<br/>内容分发网络]
        B[DNS<br/>域名解析]
    end
    
    subgraph "边界网络 Edge Network"
        C[DDoS防护<br/>DDoS Protection]
        D[WAF<br/>Web应用防火墙]
        E[负载均衡<br/>Load Balancer]
    end
    
    subgraph "应用网络 Application Network"
        F[应用子网<br/>Application Subnet<br/>10.1.0.0/24]
        G[服务发现<br/>Service Discovery]
        H[API网关<br/>API Gateway]
    end
    
    subgraph "业务网络 Business Network"
        I[业务子网<br/>Business Subnet<br/>10.2.0.0/24]
        J[微服务集群<br/>Microservices Cluster]
        K[服务间通信<br/>Inter-service Communication]
    end
    
    subgraph "数据网络 Data Network"
        L[数据子网<br/>Data Subnet<br/>10.3.0.0/24]
        M[数据库集群<br/>Database Cluster]
        N[缓存集群<br/>Cache Cluster]
        O[存储集群<br/>Storage Cluster]
    end
    
    subgraph "管理网络 Management Network"
        P[管理子网<br/>Management Subnet<br/>10.4.0.0/24]
        Q[监控系统<br/>Monitoring System]
        R[日志系统<br/>Logging System]
        S[运维工具<br/>Operations Tools]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    
    F --> G
    G --> H
    H --> I
    
    I --> J
    J --> K
    K --> L
    
    L --> M
    L --> N
    L --> O
    
    F --> P
    I --> P
    L --> P
    P --> Q
    P --> R
    P --> S
```

## 服务器配置

### Web服务器配置

| 组件 | CPU | 内存 | 磁盘 | 网络 | 数量 |
|------|-----|------|------|------|------|
| Nginx | 4核 | 8GB | 100GB SSD | 1Gbps | 2台 |
| Tomcat | 8核 | 16GB | 200GB SSD | 1Gbps | 4台 |

### 应用服务器配置

| 组件 | CPU | 内存 | 磁盘 | 网络 | 数量 |
|------|-----|------|------|------|------|
| API Gateway | 8核 | 32GB | 500GB SSD | 10Gbps | 6台 |
| Business Service | 16核 | 64GB | 1TB SSD | 10Gbps | 8台 |

### 数据服务器配置

| 组件 | CPU | 内存 | 磁盘 | 网络 | 数量 |
|------|-----|------|------|------|------|
| DB2 | 32核 | 128GB | 2TB SSD RAID10 | 10Gbps | 3台 |
| Redis | 16核 | 64GB | 1TB SSD | 10Gbps | 6台 |
| ZooKeeper | 8核 | 16GB | 500GB SSD | 1Gbps | 3台 |

## 高可用设计

### 数据库高可用

```mermaid
graph TB
    subgraph "DB2 RAC集群"
        A[DB2节点1<br/>Primary]
        B[DB2节点2<br/>Secondary]
        C[DB2节点3<br/>Standby]
        D[共享存储<br/>Shared Storage]
    end
    
    A --> D
    B --> D
    C --> D
    
    subgraph "故障转移策略"
        E[自动故障检测<br/>Automatic Failure Detection]
        F[自动故障转移<br/>Automatic Failover]
        G[数据同步<br/>Data Synchronization]
        H[负载均衡<br/>Load Balancing]
    end
```

### 应用高可用

```mermaid
graph TB
    subgraph "应用集群"
        A[应用实例1<br/>App Instance 1]
        B[应用实例2<br/>App Instance 2]
        C[应用实例3<br/>App Instance 3]
        D[应用实例N<br/>App Instance N]
    end
    
    subgraph "负载均衡器"
        E[主负载均衡<br/>Primary Load Balancer]
        F[备负载均衡<br/>Backup Load Balancer]
    end
    
    E --> A
    E --> B
    E --> C
    E --> D
    
    F --> A
    F --> B
    F --> C
    F --> D
    
    subgraph "健康检查"
        G[HTTP健康检查<br/>HTTP Health Check]
        H[TCP健康检查<br/>TCP Health Check]
        I[应用健康检查<br/>Application Health Check]
    end
```

## 灾备方案

### 同城灾备

```mermaid
graph TB
    subgraph "主数据中心 Primary Data Center"
        A[主站点<br/>Primary Site]
        B[主数据库<br/>Primary Database]
        C[主应用<br/>Primary Application]
    end
    
    subgraph "同城备份中心 Local Backup Center"
        D[备站点<br/>Backup Site]
        E[备数据库<br/>Backup Database]
        F[备应用<br/>Backup Application]
    end
    
    A -.->|实时同步<br/>Real-time Sync| D
    B -.->|数据复制<br/>Data Replication| E
    C -.->|应用同步<br/>App Sync| F
    
    subgraph "切换策略"
        G[自动切换<br/>Automatic Switchover]
        H[手动切换<br/>Manual Switchover]
        I[数据一致性检查<br/>Data Consistency Check]
    end
```

### 异地灾备

```mermaid
graph TB
    subgraph "主数据中心 Primary Data Center"
        A[生产环境<br/>Production Environment]
        B[实时数据<br/>Real-time Data]
    end
    
    subgraph "异地灾备中心 Remote Disaster Recovery Center"
        C[灾备环境<br/>Disaster Recovery Environment]
        D[备份数据<br/>Backup Data]
    end
    
    A -.->|定时同步<br/>Scheduled Sync| C
    B -.->|数据备份<br/>Data Backup| D
    
    subgraph "恢复策略"
        E[RTO目标<br/>Recovery Time Objective<br/>< 4小时]
        F[RPO目标<br/>Recovery Point Objective<br/>< 1小时]
        G[演练计划<br/>Drill Plan<br/>月度演练]
    end
```

## 部署流程

### CI/CD流程

```mermaid
graph LR
    A[代码提交<br/>Code Commit] --> B[代码检查<br/>Code Review]
    B --> C[单元测试<br/>Unit Test]
    C --> D[构建打包<br/>Build & Package]
    D --> E[集成测试<br/>Integration Test]
    E --> F[部署测试环境<br/>Deploy to Test]
    F --> G[自动化测试<br/>Automated Test]
    G --> H[部署预生产<br/>Deploy to Staging]
    H --> I[生产部署<br/>Deploy to Production]
    I --> J[健康检查<br/>Health Check]
    J --> K[监控告警<br/>Monitoring & Alert]
```

### 蓝绿部署

```mermaid
graph TB
    subgraph "负载均衡器"
        A[Load Balancer]
    end
    
    subgraph "蓝色环境 Blue Environment"
        B[当前生产版本<br/>Current Production Version]
        C[应用实例1-3<br/>App Instances 1-3]
    end
    
    subgraph "绿色环境 Green Environment"
        D[新版本<br/>New Version]
        E[应用实例4-6<br/>App Instances 4-6]
    end
    
    A --> B
    B --> C
    
    A -.->|切换<br/>Switch| D
    D -.-> E
    
    subgraph "切换步骤"
        F[1. 部署新版本到绿色环境<br/>Deploy New Version to Green]
        G[2. 健康检查<br/>Health Check]
        H[3. 流量切换<br/>Traffic Switch]
        I[4. 监控验证<br/>Monitor & Verify]
        J[5. 回滚准备<br/>Rollback Ready]
    end
```
