# YOP Center 系统架构图

## 系统整体架构

YOP Center采用分层架构设计，从上到下分为客户端层、网关层、业务层、数据层和基础设施层。

```mermaid
graph TB
    subgraph "客户端层 Client Layer"
        A[移动应用<br/>Mobile Apps] 
        B[Web应用<br/>Web Applications]
        C[第三方系统<br/>Third-party Systems]
        D[开发者SDK<br/>Developer SDKs]
    end
    
    subgraph "网关层 Gateway Layer"
        E[负载均衡器<br/>Load Balancer]
        F[API网关<br/>API Gateway]
        G[认证授权<br/>Authentication & Authorization]
        H[路由分发<br/>Routing & Dispatching]
        I[参数验证<br/>Parameter Validation]
        J[加密解密<br/>Encryption & Decryption]
        K[限流熔断<br/>Rate Limiting & Circuit Breaker]
    end
    
    subgraph "业务层 Business Layer"
        L[支付服务<br/>Payment Service]
        M[账户服务<br/>Account Service]
        N[风控服务<br/>Risk Control Service]
        O[通知服务<br/>Notification Service]
        P[第三方集成<br/>Third-party Integration]
    end
    
    subgraph "数据层 Data Layer"
        Q[(核心数据库<br/>Core Database<br/>DB2)]
        R[(缓存层<br/>Cache Layer<br/>Redis)]
        S[(配置中心<br/>Configuration Center<br/>ZooKeeper)]
        T[(消息队列<br/>Message Queue)]
    end
    
    subgraph "基础设施层 Infrastructure Layer"
        U[监控系统<br/>Monitoring System]
        V[日志系统<br/>Logging System]
        W[配置管理<br/>Configuration Management]
        X[服务注册发现<br/>Service Registry & Discovery]
    end
    
    %% 连接关系
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    
    G --> L
    H --> M
    I --> N
    J --> O
    K --> P
    
    L --> Q
    M --> Q
    N --> R
    O --> R
    P --> S
    
    F --> U
    F --> V
    S --> W
    S --> X
    
    %% 样式定义
    classDef clientLayer fill:#e1f5fe
    classDef gatewayLayer fill:#f3e5f5
    classDef businessLayer fill:#e8f5e8
    classDef dataLayer fill:#fff3e0
    classDef infraLayer fill:#fce4ec
    
    class A,B,C,D clientLayer
    class E,F,G,H,I,J,K gatewayLayer
    class L,M,N,O,P businessLayer
    class Q,R,S,T dataLayer
    class U,V,W,X infraLayer
```

## 核心模块架构

```mermaid
graph TB
    subgraph "yop-center-parent"
        subgraph "核心框架 Core Framework"
            A[yop-frame<br/>框架核心]
            B[yop-center<br/>主应用]
            C[yop-router<br/>路由模块]
        end
        
        subgraph "扩展模块 Extension Modules"
            D[yop-ext-api<br/>扩展API]
            E[yop-ext-utils<br/>扩展工具]
        end
        
        subgraph "第三方集成 Third-party Integration"
            F[yop-ext-douyin<br/>抖音集成]
            G[yop-ext-xmgj<br/>厦门国际集成]
            H[yop-ext-nbyh<br/>宁波银行集成]
        end
    end
    
    %% 依赖关系
    B --> A
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    
    C --> A
    
    F --> D
    F --> E
    G --> D
    G --> E
    H --> D
    H --> E
    
    E --> D
    
    %% 样式
    classDef coreModule fill:#bbdefb
    classDef extModule fill:#c8e6c9
    classDef integrationModule fill:#ffcdd2
    
    class A,B,C coreModule
    class D,E extModule
    class F,G,H integrationModule
```

## 接口类图

```mermaid
classDiagram
    class ApiRouter {
        +register(key: String, type: String) ApiHandler
        +route(path: String, method: String, appId: String) ApiHandler
        +route(apiGroup: String, path: String, method: String, appId: String, customerNo: String) ApiHandler
    }
    
    class ApiHandler {
        +handle(exchange: ServerWebExchange) Response
        +getApiDefinition() ApiDefinition
        +getApiDTO() ApiNewDTO
        +getApiBackend() ApiBackend
        +isApiV2() boolean
        +isGeneric() boolean
    }
    
    class Router {
        +route(context: RouteContext) Object
        +responseMapping(rawResponse: Map) Map
        +type() String
    }
    
    class HttpRouter {
        -url: String
        -httpMethod: HttpMethod
        -contentType: String
        +doRoute(context: RouteContext) Object
    }
    
    class DubboRouter {
        -serviceName: String
        -methodName: String
        -version: String
        +doRoute(context: RouteContext) Object
    }
    
    class ApiContextParser {
        +parse(exchange: ServerWebExchange) ApiContext
        +getProtocolMaker() ProtocolMakerEnum
    }
    
    class ServerWebExchange {
        +getRequest() HttpServletRequest
        +getResponse() HttpServletResponse
        +getAttributes() Map
    }
    
    ApiRouter --> ApiHandler : creates
    ApiHandler --> Router : uses
    Router <|-- HttpRouter
    Router <|-- DubboRouter
    ApiContextParser --> ServerWebExchange : parses
    ApiHandler --> ServerWebExchange : handles
```

## 业务流程时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant Router as 路由器
    participant Backend as 后端服务
    participant DB as 数据库
    participant Cache as 缓存
    
    Client->>Gateway: 1. 发送API请求
    Gateway->>Gateway: 2. 解析请求参数
    Gateway->>Auth: 3. 验证签名/Token
    Auth-->>Gateway: 4. 返回认证结果
    
    alt 认证成功
        Gateway->>Gateway: 5. 参数验证
        Gateway->>Router: 6. 路由分发
        Router->>Backend: 7. 调用后端服务
        
        Backend->>Cache: 8. 查询缓存
        alt 缓存命中
            Cache-->>Backend: 9. 返回缓存数据
        else 缓存未命中
            Backend->>DB: 10. 查询数据库
            DB-->>Backend: 11. 返回数据
            Backend->>Cache: 12. 更新缓存
        end
        
        Backend-->>Router: 13. 返回业务结果
        Router-->>Gateway: 14. 返回路由结果
        Gateway->>Gateway: 15. 响应加密/签名
        Gateway-->>Client: 16. 返回最终响应
    else 认证失败
        Gateway-->>Client: 返回认证错误
    end
```

## 关键设计原则

### 1. 分层架构
- **职责分离**：每层专注于特定功能
- **松耦合**：层间通过接口交互
- **可扩展**：支持水平和垂直扩展

### 2. 微服务架构
- **服务自治**：每个服务独立部署和扩展
- **技术多样性**：不同服务可选择合适的技术栈
- **故障隔离**：单个服务故障不影响整体系统

### 3. 高可用设计
- **无单点故障**：关键组件都有冗余
- **故障转移**：自动检测和切换
- **降级策略**：核心功能优先保障

### 4. 安全设计
- **多层防护**：网络、应用、数据多层安全
- **最小权限**：按需分配访问权限
- **审计追踪**：完整的操作日志记录

## 技术选型说明

### 框架选择
- **Spring Framework**：成熟的企业级框架，丰富的生态
- **Maven**：标准的项目管理和构建工具

### 数据存储
- **DB2**：企业级关系数据库，高可靠性
- **Redis**：高性能缓存，支持多种数据结构

### 服务治理
- **ZooKeeper**：分布式协调服务，配置管理
- **Hessian**：轻量级RPC框架，性能优秀

### 监控运维
- **自研监控**：定制化监控指标和告警
- **日志聚合**：统一日志收集和分析
