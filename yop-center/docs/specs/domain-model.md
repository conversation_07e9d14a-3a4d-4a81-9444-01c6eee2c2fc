# 领域模型 - 转换引擎核心概念

## 核心对象

1.  **MappingRuleSet (映射规则集)**
    * 描述：代表一个完整的从源JSON到目标DTO的映射定义。通常对应一个配置文件。
    * 属性：
        * `sourceIdentifier` (string): 源系统的唯一标识。
        * `targetDtoClass` (string): 目标DTO的完全限定类名。
        * `fieldMappings` (List<FieldMapping>): 字段映射规则列表。

2.  **FieldMapping (字段映射规则)**
    * 描述：定义单个字段的映射逻辑。
    * 属性：
        * `sourcePath` (string): 源字段的JSON Path路径 (e.g., "$.order.details[0].price")。
        * `targetField` (string): 目标DTO的字段名 (e.g., "price")。
        * `transformer` (string): (可选) 应用于该字段的值转换器名称 (e.g., "centsToDecimal")。
        * `transformerArgs` (Map<String, any>): (可选) 传递给转换器的额外参数 (e.g., for dates: `{"format": "yyyy/MM/dd HH:mm:ss"}`）。
        * `defaultValue` (any): (可选) 源字段不存在时使用的默认值。

3.  **ValueTransformer (值转换器)**
    * 描述：一个无状态的函数，负责将输入值转换为输出值。这是一个接口或函数式接口。
    * 核心方法： `Object transform(Object sourceValue, Map<String, any> args)`
    * **实现举例**:
        * `UnixTimestampToDateTransformer`: 将Long时间戳转为Date。
        * `StringToDateTransformer`: 根据传入的格式化字符串解析日期。
        * `CentsToDecimalTransformer`: 将Integer或Long的“分”转为BigDecimal的“元”。
        * `EnumTransformer`: 根据参数中的目标Enum类，将字符串转为对应的枚举实例。
        * `DictionaryTransformer`: 根据参数中提供的字典数据，进行键值映射。